<?php
/**
 * DeepSeek API接口
 * 提供DeepSeek AI配置管理和对话功能的独立API接口
 * 
 * @version 1.0.0
 * <AUTHOR>
 */

// 定义API访问常量
define('API_ACCESS', true);

// 错误处理
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', dirname(__DIR__) . '/logs/deepseek_api_errors.log');

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 加载必要的文件
require_once __DIR__ . '/config.php';
require_once __DIR__ . '/api_base.php';

/**
 * DeepSeek API类
 */
class DeepSeekApi extends ApiBase {
    private $deepseekApiUrl = 'https://api.deepseek.com/v1/chat/completions';
    private $supportedModels = [
        'deepseek-chat' => 'DeepSeek Chat',
        'deepseek-reasoner' => 'DeepSeek-R1-0528'
    ];

    /**
     * 构造函数
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * 处理请求
     */
    public function handleRequest() {
        // 解析请求路径
        $path = $_SERVER['PATH_INFO'] ?? '';
        $path = trim($path, '/');
        
        // 分割路径获取端点和操作
        $segments = explode('/', $path);
        $endpoint = $segments[0] ?? '';
        $action = $segments[1] ?? '';
        
        // 检查端点是否存在
        if (empty($endpoint)) {
            return $this->respondError('Invalid endpoint', 404);
        }

        // 路由处理
        switch ($endpoint) {
            case 'config':
                return $this->handleConfigRequest($action);
            case 'chat':
                return $this->handleChatRequest($action);
            case 'models':
                return $this->handleModelsRequest($action);
            case 'status':
                return $this->handleStatusRequest();
            default:
                return $this->respondError('Endpoint not found', 404);
        }
    }

    /**
     * 处理配置请求
     */
    private function handleConfigRequest($action) {
        switch ($this->method) {
            case 'GET':
                return $this->getConfig($action);
            case 'POST':
                return $this->saveConfig($action);
            case 'PUT':
                return $this->updateConfig($action);
            case 'DELETE':
                return $this->deleteConfig($action);
            default:
                return $this->respondError('Method not allowed', 405);
        }
    }

    /**
     * 获取配置
     */
    private function getConfig($action) {
        try {
            switch ($action) {
                case 'all':
                    // 获取所有DeepSeek配置
                    $config = $this->getAllDeepSeekConfig();
                    return $this->respondSuccess($config, '获取配置成功');
                    
                case 'keys':
                    // 获取API密钥列表
                    $keys = $this->getApiKeys();
                    return $this->respondSuccess($keys, '获取API密钥列表成功');
                    
                case 'settings':
                    // 获取基本设置
                    $settings = $this->getBasicSettings();
                    return $this->respondSuccess($settings, '获取基本设置成功');
                    
                default:
                    return $this->respondError('Invalid config action', 400);
            }
        } catch (Exception $e) {
            error_log("获取DeepSeek配置失败: " . $e->getMessage());
            return $this->respondError('获取配置失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 保存配置
     */
    private function saveConfig($action) {
        try {
            switch ($action) {
                case 'key':
                    // 添加API密钥
                    $apiKey = $this->request['api_key'] ?? '';
                    $keyName = $this->request['key_name'] ?? 'DeepSeek API Key';
                    
                    if (empty($apiKey)) {
                        return $this->respondError('API密钥不能为空', 400);
                    }
                    
                    $result = $this->addApiKey($apiKey, $keyName);
                    return $this->respondSuccess($result, 'API密钥添加成功');
                    
                case 'settings':
                    // 保存基本设置
                    $settings = $this->request['settings'] ?? [];
                    $result = $this->saveBasicSettings($settings);
                    return $this->respondSuccess($result, '设置保存成功');
                    
                case 'all':
                    // 保存完整配置
                    $config = $this->request['config'] ?? [];
                    $result = $this->saveCompleteConfig($config);
                    return $this->respondSuccess($result, '配置保存成功');
                    
                default:
                    return $this->respondError('Invalid config action', 400);
            }
        } catch (Exception $e) {
            error_log("保存DeepSeek配置失败: " . $e->getMessage());
            return $this->respondError('保存配置失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 更新配置
     */
    private function updateConfig($action) {
        try {
            switch ($action) {
                case 'key':
                    // 更新API密钥状态
                    $keyId = $this->request['key_id'] ?? 0;
                    $isActive = $this->request['is_active'] ?? true;
                    
                    $result = $this->updateApiKeyStatus($keyId, $isActive);
                    return $this->respondSuccess($result, 'API密钥状态更新成功');
                    
                case 'settings':
                    // 更新基本设置
                    $settings = $this->request['settings'] ?? [];
                    $result = $this->updateBasicSettings($settings);
                    return $this->respondSuccess($result, '设置更新成功');
                    
                default:
                    return $this->respondError('Invalid config action', 400);
            }
        } catch (Exception $e) {
            error_log("更新DeepSeek配置失败: " . $e->getMessage());
            return $this->respondError('更新配置失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 删除配置
     */
    private function deleteConfig($action) {
        try {
            switch ($action) {
                case 'key':
                    // 删除API密钥
                    $keyId = $this->request['key_id'] ?? 0;
                    
                    if (empty($keyId)) {
                        return $this->respondError('密钥ID不能为空', 400);
                    }
                    
                    $result = $this->deleteApiKey($keyId);
                    return $this->respondSuccess($result, 'API密钥删除成功');
                    
                case 'all':
                    // 清空所有配置
                    $result = $this->clearAllConfig();
                    return $this->respondSuccess($result, '配置清空成功');
                    
                default:
                    return $this->respondError('Invalid config action', 400);
            }
        } catch (Exception $e) {
            error_log("删除DeepSeek配置失败: " . $e->getMessage());
            return $this->respondError('删除配置失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 处理聊天请求
     */
    private function handleChatRequest($action) {
        if ($this->method !== 'POST') {
            return $this->respondError('Method not allowed', 405);
        }

        try {
            switch ($action) {
                case 'completions':
                    // 聊天对话
                    return $this->chatCompletions();
                    
                case 'validate':
                    // 验证API密钥
                    return $this->validateApiKey();
                    
                default:
                    return $this->respondError('Invalid chat action', 400);
            }
        } catch (Exception $e) {
            error_log("处理聊天请求失败: " . $e->getMessage());
            return $this->respondError('聊天请求失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 处理模型请求
     */
    private function handleModelsRequest($action) {
        if ($this->method !== 'GET') {
            return $this->respondError('Method not allowed', 405);
        }

        try {
            switch ($action) {
                case 'list':
                case '':
                    // 获取支持的模型列表
                    return $this->respondSuccess($this->supportedModels, '获取模型列表成功');
                    
                default:
                    return $this->respondError('Invalid models action', 400);
            }
        } catch (Exception $e) {
            error_log("获取模型列表失败: " . $e->getMessage());
            return $this->respondError('获取模型列表失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 处理状态请求
     */
    private function handleStatusRequest() {
        if ($this->method !== 'GET') {
            return $this->respondError('Method not allowed', 405);
        }

        try {
            $status = [
                'service' => 'DeepSeek API',
                'version' => '1.0.0',
                'status' => 'active',
                'timestamp' => date('Y-m-d H:i:s'),
                'supported_models' => array_keys($this->supportedModels),
                'endpoints' => [
                    'config' => '/config/{action}',
                    'chat' => '/chat/{action}',
                    'models' => '/models/{action}',
                    'status' => '/status'
                ]
            ];

            return $this->respondSuccess($status, 'DeepSeek API服务正常');
        } catch (Exception $e) {
            error_log("获取服务状态失败: " . $e->getMessage());
            return $this->respondError('获取服务状态失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取所有DeepSeek配置
     */
    private function getAllDeepSeekConfig() {
        $stmt = $this->safeQuery("
            SELECT * FROM deepseek_config
            WHERE is_active = 1
            ORDER BY created_at DESC
        ");

        $configs = $stmt->fetchAll();

        // 如果没有配置，返回默认配置
        if (empty($configs)) {
            return [
                'enabled' => false,
                'api_keys' => [],
                'current_model' => 'deepseek-chat',
                'deep_thinking_enabled' => false,
                'reply_delay' => 0,
                'system_prompt' => '你是一名专业的微信小店客服人员，请用自然、友好的语言回答用户问题，回答要口语化且不超过50字。注意：不要提及自己是机器人或AI助手，要表现出真人的服务态度。'
            ];
        }

        $config = $configs[0];

        // 获取API密钥
        $apiKeys = $this->getApiKeys();

        return [
            'id' => $config['id'],
            'enabled' => (bool)$config['enabled'],
            'api_keys' => $apiKeys,
            'current_model' => $config['current_model'],
            'deep_thinking_enabled' => (bool)$config['deep_thinking_enabled'],
            'reply_delay' => (int)$config['reply_delay'],
            'system_prompt' => $config['system_prompt'],
            'created_at' => $config['created_at'],
            'updated_at' => $config['updated_at']
        ];
    }

    /**
     * 获取API密钥列表
     */
    private function getApiKeys() {
        $stmt = $this->safeQuery("
            SELECT id, key_name,
                   CONCAT(LEFT(api_key, 8), '...') as masked_key,
                   is_active, is_valid, last_validated_at, created_at
            FROM deepseek_api_keys
            WHERE is_deleted = 0
            ORDER BY created_at DESC
        ");

        return $stmt->fetchAll();
    }

    /**
     * 获取基本设置
     */
    private function getBasicSettings() {
        $stmt = $this->safeQuery("
            SELECT enabled, current_model, deep_thinking_enabled,
                   reply_delay, system_prompt
            FROM deepseek_config
            WHERE is_active = 1
            ORDER BY created_at DESC
            LIMIT 1
        ");

        $settings = $stmt->fetch();

        if (!$settings) {
            return [
                'enabled' => false,
                'current_model' => 'deepseek-chat',
                'deep_thinking_enabled' => false,
                'reply_delay' => 0,
                'system_prompt' => '你是一名专业的微信小店客服人员，请用自然、友好的语言回答用户问题，回答要口语化且不超过50字。注意：不要提及自己是机器人或AI助手，要表现出真人的服务态度。'
            ];
        }

        return [
            'enabled' => (bool)$settings['enabled'],
            'current_model' => $settings['current_model'],
            'deep_thinking_enabled' => (bool)$settings['deep_thinking_enabled'],
            'reply_delay' => (int)$settings['reply_delay'],
            'system_prompt' => $settings['system_prompt']
        ];
    }

    /**
     * 添加API密钥
     */
    private function addApiKey($apiKey, $keyName) {
        // 验证API密钥格式
        if (!$this->validateApiKeyFormat($apiKey)) {
            throw new Exception('API密钥格式不正确');
        }

        // 检查密钥是否已存在
        $stmt = $this->safeQuery("
            SELECT id FROM deepseek_api_keys
            WHERE api_key = ? AND is_deleted = 0
        ", [$apiKey]);

        if ($stmt->fetch()) {
            throw new Exception('API密钥已存在');
        }

        // 验证API密钥有效性
        $isValid = $this->testApiKey($apiKey);

        // 插入新密钥
        $stmt = $this->safeQuery("
            INSERT INTO deepseek_api_keys
            (key_name, api_key, is_active, is_valid, last_validated_at, created_at)
            VALUES (?, ?, 1, ?, NOW(), NOW())
        ", [$keyName, $apiKey, $isValid ? 1 : 0]);

        $keyId = $this->db->lastInsertId();

        return [
            'id' => $keyId,
            'key_name' => $keyName,
            'masked_key' => substr($apiKey, 0, 8) . '...',
            'is_valid' => $isValid,
            'message' => $isValid ? 'API密钥验证成功' : 'API密钥验证失败，但已保存'
        ];
    }

    /**
     * 保存基本设置
     */
    private function saveBasicSettings($settings) {
        $enabled = $settings['enabled'] ?? false;
        $currentModel = $settings['current_model'] ?? 'deepseek-chat';
        $deepThinkingEnabled = $settings['deep_thinking_enabled'] ?? false;
        $replyDelay = $settings['reply_delay'] ?? 0;
        $systemPrompt = $settings['system_prompt'] ?? '';

        // 验证模型是否支持
        if (!isset($this->supportedModels[$currentModel])) {
            throw new Exception('不支持的模型: ' . $currentModel);
        }

        // 检查是否已有配置
        $stmt = $this->safeQuery("
            SELECT id FROM deepseek_config
            WHERE is_active = 1
            ORDER BY created_at DESC
            LIMIT 1
        ");

        $existingConfig = $stmt->fetch();

        if ($existingConfig) {
            // 更新现有配置
            $this->safeQuery("
                UPDATE deepseek_config
                SET enabled = ?, current_model = ?, deep_thinking_enabled = ?,
                    reply_delay = ?, system_prompt = ?, updated_at = NOW()
                WHERE id = ?
            ", [$enabled ? 1 : 0, $currentModel, $deepThinkingEnabled ? 1 : 0,
                $replyDelay, $systemPrompt, $existingConfig['id']]);

            $configId = $existingConfig['id'];
        } else {
            // 创建新配置
            $this->safeQuery("
                INSERT INTO deepseek_config
                (enabled, current_model, deep_thinking_enabled, reply_delay,
                 system_prompt, is_active, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, 1, NOW(), NOW())
            ", [$enabled ? 1 : 0, $currentModel, $deepThinkingEnabled ? 1 : 0,
                $replyDelay, $systemPrompt]);

            $configId = $this->db->lastInsertId();
        }

        return [
            'id' => $configId,
            'enabled' => $enabled,
            'current_model' => $currentModel,
            'deep_thinking_enabled' => $deepThinkingEnabled,
            'reply_delay' => $replyDelay,
            'system_prompt' => $systemPrompt
        ];
    }
