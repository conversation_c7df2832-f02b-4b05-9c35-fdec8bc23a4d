<?php
/**
 * 独立弹窗API接口 - 重构版本
 * 处理APP弹窗相关请求，使用统一的MySQL数据库配置
 */

// 引入统一数据库配置
require_once __DIR__ . '/database_config.php';

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, User-Agent');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 错误处理
function sendError($message, $code = 500) {
    http_response_code($code);
    echo json_encode([
        'success' => false,
        'message' => $message,
        'popup' => null,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    exit;
}

// 成功响应
function sendSuccess($popup = null, $message = 'Success') {
    echo json_encode([
        'success' => true,
        'message' => $message,
        'popup' => $popup,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    exit;
}

// 初始化弹窗表
function initPopupTable($db) {
    try {
        $pdo = $db->getConnection();
        if (!$pdo) return;
        
        $sql = "CREATE TABLE IF NOT EXISTS app_popups (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(255) NOT NULL,
            content TEXT NOT NULL,
            type JSON,
            custom_days INT DEFAULT NULL,
            status ENUM('active', 'inactive') DEFAULT 'active',
            priority INT DEFAULT 1,
            target_audience ENUM('all', 'new', 'existing') DEFAULT 'all',
            display_count INT DEFAULT 0,
            click_count INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_status (status),
            INDEX idx_priority (priority)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        
        $pdo->exec($sql);
        
        // 不再自动插入默认弹窗
        // 只有后台手动创建的弹窗才会显示
    } catch (Exception $e) {
        // 忽略表创建错误，继续执行
        error_log("初始化弹窗表失败: " . $e->getMessage());
    }
}

// 获取备用弹窗数据
function getFallbackPopup() {
    return [
        'id' => 'fallback',
        'title' => '🚀 小梅花AI智能客服',
        'content' => '<div class="popup-content-body">
            <p class="popup-description">欢迎使用小梅花AI智能客服！</p>
            <div class="feature-list">
                <div class="feature-item">
                    <span class="check-icon">✓</span>
                    <span class="feature-text">AI智能对话，24小时在线服务</span>
                </div>
                <div class="feature-item">
                    <span class="check-icon">✓</span>
                    <span class="feature-text">多店铺管理，统一客服平台</span>
                </div>
                <div class="feature-item">
                    <span class="check-icon">✓</span>
                    <span class="feature-text">知识库管理，快速问答匹配</span>
                </div>
                <div class="feature-item">
                    <span class="check-icon">✓</span>
                    <span class="feature-text">数据分析，客服效果一目了然</span>
                </div>
            </div>
        </div>',
        'type' => ['once'],
        'custom_days' => null,
        'status' => 'active',
        'priority' => 10,
        'target_audience' => 'all',
        'display_count' => 0,
        'click_count' => 0,
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ];
}

// 获取活跃弹窗
function getActivePopup() {
    $db = getDatabase();
    $pdo = $db->getConnection();

    // 如果数据库连接失败，返回null
    if (!$pdo) {
        return null;
    }

    try {
        // 初始化弹窗表
        initPopupTable($db);

        // 获取状态为active的弹窗，按优先级和创建时间排序
        $stmt = $pdo->prepare("
            SELECT * FROM app_popups
            WHERE status = 'active'
            ORDER BY priority DESC, created_at DESC
            LIMIT 1
        ");
        $stmt->execute();
        $popup = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($popup) {
            // 处理type字段（可能是JSON格式）
            if (is_string($popup['type'])) {
                $decoded = json_decode($popup['type'], true);
                $popup['type'] = $decoded ?: [$popup['type']];
            }

            // 增加显示次数
            try {
                $updateStmt = $pdo->prepare("
                    UPDATE app_popups
                    SET display_count = display_count + 1
                    WHERE id = ?
                ");
                $updateStmt->execute([$popup['id']]);
            } catch (Exception $e) {
                // 忽略更新错误
            }

            return $popup;
        }

        // 数据库中没有活跃弹窗，返回null
        return null;
    } catch (Exception $e) {
        // 查询失败时返回null，不显示任何弹窗
        error_log("获取弹窗失败: " . $e->getMessage());
        return null;
    }
}

// 主处理逻辑
try {
    // 只处理GET请求
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        sendError('只支持GET请求', 405);
    }
    
    // 获取活跃弹窗
    $popup = getActivePopup();
    
    if ($popup) {
        sendSuccess($popup, 'Active popup found');
    } else {
        sendSuccess(null, 'No active popup found');
    }
    
} catch (Exception $e) {
    sendError('服务器内部错误: ' . $e->getMessage());
}
?>
