<?php
/**
 * 卡密检查API - 仅检查卡密是否需要绑定手机号码
 * 不进行完整的验证，只返回绑定状态
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 引入数据库配置
require_once '../config/database.php';

// 获取请求数据
$input = json_decode(file_get_contents('php://input'), true);
$key = isset($input['key']) ? trim($input['key']) : '';

// 获取客户端IP
$client_ip = $_SERVER['HTTP_X_FORWARDED_FOR'] ?? $_SERVER['HTTP_X_REAL_IP'] ?? $_SERVER['REMOTE_ADDR'] ?? 'unknown';

// 记录API调用日志的函数
function logApiCall($key, $ip, $status, $message) {
    $masked_key = !empty($key) ? substr($key, 0, 4) . '****' . substr($key, -4) : '空';
    error_log("卡密检查API: key={$masked_key}, IP={$ip}, status={$status}, message={$message}");
}

// 输入验证
if (empty($key)) {
    logApiCall('', $client_ip, 'error', '卡密不能为空');
    echo json_encode([
        'success' => false, 
        'message' => '卡密不能为空',
        'error_code' => 'EMPTY_KEY'
    ]);
    exit();
}

// 限制卡密长度，避免缓冲区溢出攻击
if (strlen($key) > 255) {
    logApiCall(substr($key, 0, 20) . '...', $client_ip, 'error', '卡密格式无效');
    echo json_encode([
        'success' => false, 
        'message' => '卡密格式无效',
        'error_code' => 'INVALID_KEY_FORMAT'
    ]);
    exit();
}

try {
    // 连接数据库
    $pdo = new PDO($dsn, $username, $password, $options);
    
    // 【修复】查询卡密信息 - 使用BINARY进行严格的大小写敏感比较
    $stmt = $pdo->prepare("SELECT id, key_value, phone_number, status, expiry_date FROM license_keys WHERE BINARY key_value = ?");
    $stmt->execute([$key]);
    $license = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$license) {
        logApiCall($key, $client_ip, 'error', '卡密不存在');
        echo json_encode([
            'success' => false,
            'message' => '卡密不存在',
            'error_code' => 'KEY_NOT_FOUND'
        ]);
        exit();
    }
    
    // 检查卡密状态
    if ($license['status'] !== 'active') {
        logApiCall($key, $client_ip, 'error', '卡密已被禁用');
        echo json_encode([
            'success' => false,
            'message' => '卡密已被禁用',
            'error_code' => 'KEY_DISABLED'
        ]);
        exit();
    }
    
    // 检查卡密是否过期
    if (strtotime($license['expiry_date']) <= time()) {
        logApiCall($key, $client_ip, 'error', '卡密已过期');
        echo json_encode([
            'success' => false,
            'message' => '卡密已过期',
            'error_code' => 'KEY_EXPIRED'
        ]);
        exit();
    }
    
    // 检查是否需要绑定手机号码
    $needsPhoneBinding = empty($license['phone_number']);
    
    logApiCall($key, $client_ip, 'success', $needsPhoneBinding ? '需要绑定手机号码' : '已绑定手机号码');
    
    echo json_encode([
        'success' => true,
        'needs_phone_binding' => $needsPhoneBinding,
        'message' => $needsPhoneBinding ? '该卡密需要绑定手机号码' : '卡密状态正常'
    ]);
    
} catch (PDOException $e) {
    logApiCall($key, $client_ip, 'error', '数据库连接失败: ' . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => '服务器内部错误，请稍后重试',
        'error_code' => 'DATABASE_ERROR'
    ]);
} catch (Exception $e) {
    logApiCall($key, $client_ip, 'error', '未知错误: ' . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => '服务器内部错误，请稍后重试',
        'error_code' => 'UNKNOWN_ERROR'
    ]);
}
?>
