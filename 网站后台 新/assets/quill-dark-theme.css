/**
 * Quill.js 深色主题样式
 * 专为小梅花AI客服系统设计
 */

/* 编辑器容器 */
.quill-editor-container {
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    border-radius: 8px !important;
    background: rgba(0, 0, 0, 0.6) !important;
    overflow: visible !important;
    position: relative !important;
}

/* 编辑器主体 */
.quill-editor {
    background: rgba(0, 0, 0, 0.6) !important;
    color: white !important;
}

/* 编辑器内容区域 */
.ql-editor {
    background: rgba(0, 0, 0, 0.6) !important;
    color: white !important;
    border: none !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    font-size: 14px !important;
    line-height: 1.6 !important;
}

/* 编辑器内容文字 */
.ql-editor p,
.ql-editor div,
.ql-editor span,
.ql-editor h1,
.ql-editor h2,
.ql-editor h3,
.ql-editor h4,
.ql-editor h5,
.ql-editor h6,
.ql-editor ul,
.ql-editor ol,
.ql-editor li {
    color: white !important;
}

/* 占位符文字 */
.ql-editor::before {
    color: rgba(255, 255, 255, 0.6) !important;
    font-style: italic !important;
}

/* 工具栏样式 */
.ql-toolbar {
    background: rgba(0, 0, 0, 0.7) !important;
    border: none !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
    padding: 8px !important;
    position: relative !important;
    overflow: visible !important;
}

/* 工具栏按钮 */
.ql-toolbar button {
    width: 28px !important;
    height: 28px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 4px !important;
    margin: 2px !important;
    background: transparent !important;
    transition: all 0.2s ease !important;
}

/* 工具栏按钮悬停效果 */
.ql-toolbar button:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(255, 255, 255, 0.4) !important;
}

/* 工具栏按钮激活状态 */
.ql-toolbar button.ql-active {
    background: rgba(255, 107, 157, 0.3) !important;
    border-color: #ff6b9d !important;
}

/* 工具栏图标颜色 */
.ql-toolbar .ql-stroke {
    stroke: rgba(255, 255, 255, 0.8) !important;
}

.ql-toolbar .ql-fill {
    fill: rgba(255, 255, 255, 0.8) !important;
}

/* 下拉选择器 */
.ql-toolbar .ql-picker {
    color: rgba(255, 255, 255, 0.8) !important;
    position: relative !important;
}

.ql-toolbar .ql-picker-label {
    color: rgba(255, 255, 255, 0.8) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 4px !important;
    padding: 4px 8px !important;
    background: transparent !important;
    min-width: 28px !important;
    height: 28px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.ql-toolbar .ql-picker-label:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(255, 255, 255, 0.4) !important;
}

/* 下拉菜单选项 */
.ql-picker-options {
    background: rgba(0, 0, 0, 0.95) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 4px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
    max-height: 200px !important;
    overflow-y: auto !important;
    position: absolute !important;
    z-index: 9999 !important;
    top: 100% !important;
    left: 0 !important;
    min-width: 120px !important;
    margin-top: 2px !important;
}

.ql-picker-item {
    color: white !important;
    padding: 8px 12px !important;
    transition: background 0.2s ease !important;
}

.ql-picker-item:hover {
    background: rgba(255, 255, 255, 0.1) !important;
}

.ql-picker-item.ql-selected {
    background: rgba(255, 107, 157, 0.3) !important;
}

/* 字体大小选择器 */
.ql-size .ql-picker-label::before,
.ql-size .ql-picker-item::before {
    content: '14px' !important;
}

.ql-size .ql-picker-label[data-value="small"]::before,
.ql-size .ql-picker-item[data-value="small"]::before {
    content: '10px' !important;
}

.ql-size .ql-picker-label[data-value="large"]::before,
.ql-size .ql-picker-item[data-value="large"]::before {
    content: '18px' !important;
}

.ql-size .ql-picker-label[data-value="huge"]::before,
.ql-size .ql-picker-item[data-value="huge"]::before {
    content: '32px' !important;
}

/* 字体选择器 */
.ql-font .ql-picker-label::before,
.ql-font .ql-picker-item::before {
    content: '字体' !important;
}

/* 标题选择器 */
.ql-header .ql-picker-label::before,
.ql-header .ql-picker-item::before {
    content: '正文' !important;
}

.ql-header .ql-picker-label[data-value="1"]::before,
.ql-header .ql-picker-item[data-value="1"]::before {
    content: '标题1' !important;
}

.ql-header .ql-picker-label[data-value="2"]::before,
.ql-header .ql-picker-item[data-value="2"]::before {
    content: '标题2' !important;
}

.ql-header .ql-picker-label[data-value="3"]::before,
.ql-header .ql-picker-item[data-value="3"]::before {
    content: '标题3' !important;
}

/* 对齐方式选择器 */
.ql-align .ql-picker-label::before,
.ql-align .ql-picker-item::before {
    content: '左对齐' !important;
}

.ql-align .ql-picker-label[data-value="center"]::before,
.ql-align .ql-picker-item[data-value="center"]::before {
    content: '居中' !important;
}

.ql-align .ql-picker-label[data-value="right"]::before,
.ql-align .ql-picker-item[data-value="right"]::before {
    content: '右对齐' !important;
}

.ql-align .ql-picker-label[data-value="justify"]::before,
.ql-align .ql-picker-item[data-value="justify"]::before {
    content: '两端对齐' !important;
}

/* 颜色选择器 */
.ql-color .ql-picker-label,
.ql-background .ql-picker-label {
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 4px !important;
    width: 28px !important;
    height: 28px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* 颜色选择器下拉菜单 */
.ql-color .ql-picker-options,
.ql-background .ql-picker-options {
    position: absolute !important;
    z-index: 10000 !important;
    top: 100% !important;
    left: 0 !important;
    min-width: 150px !important;
    max-width: 200px !important;
    margin-top: 2px !important;
    padding: 8px !important;
    background: rgba(0, 0, 0, 0.95) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 6px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4) !important;
}

/* 对齐方式选择器 */
.ql-align .ql-picker-label {
    width: 28px !important;
    height: 28px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.ql-align .ql-picker-options {
    position: absolute !important;
    z-index: 10000 !important;
    top: 100% !important;
    left: 0 !important;
    min-width: 100px !important;
    margin-top: 2px !important;
    background: rgba(0, 0, 0, 0.95) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 6px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4) !important;
}

/* 链接样式 */
.ql-editor a {
    color: #4ecdc4 !important;
    text-decoration: underline !important;
}

.ql-editor a:hover {
    color: #45b7aa !important;
}

/* 引用块样式 */
.ql-editor blockquote {
    border-left: 4px solid rgba(255, 107, 157, 0.6) !important;
    background: rgba(255, 255, 255, 0.05) !important;
    padding: 10px 15px !important;
    margin: 10px 0 !important;
    color: rgba(255, 255, 255, 0.9) !important;
}

/* 代码块样式 */
.ql-editor pre {
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 4px !important;
    padding: 10px !important;
    color: #4ecdc4 !important;
    font-family: 'Courier New', monospace !important;
}

/* 列表样式 */
.ql-editor ul,
.ql-editor ol {
    padding-left: 20px !important;
}

.ql-editor li {
    margin: 5px 0 !important;
}

/* 滚动条样式 */
.ql-editor::-webkit-scrollbar {
    width: 8px !important;
}

.ql-editor::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1) !important;
    border-radius: 4px !important;
}

.ql-editor::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3) !important;
    border-radius: 4px !important;
}

.ql-editor::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5) !important;
}

/* 强制修复下拉菜单溢出问题 - 最高优先级 */
.ql-toolbar .ql-picker {
    overflow: visible !important;
    position: relative !important;
}

.ql-toolbar .ql-picker.ql-expanded .ql-picker-options {
    display: block !important;
}

/* 强制所有下拉菜单都在容器内显示 */
.ql-picker-options {
    position: absolute !important;
    z-index: 99999 !important;
    top: calc(100% + 2px) !important;
    left: 0 !important;
    transform: none !important;
    margin: 0 !important;
    max-height: 200px !important;
    overflow-y: auto !important;
    white-space: nowrap !important;
}

/* 防止下拉菜单被父容器裁剪 */
.quill-editor-container,
.ql-toolbar,
.ql-container {
    overflow: visible !important;
}

/* 颜色选择器特殊处理 - 强制在容器内 */
.ql-color .ql-picker-options,
.ql-background .ql-picker-options {
    position: absolute !important;
    z-index: 99999 !important;
    top: calc(100% + 2px) !important;
    left: 0 !important;
    transform: none !important;
    margin: 0 !important;
    max-width: 180px !important;
    width: 180px !important;
    padding: 8px !important;
    background: rgba(0, 0, 0, 0.95) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 6px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4) !important;
}

/* 对齐选择器特殊处理 - 强制在容器内 */
.ql-align .ql-picker-options {
    position: absolute !important;
    z-index: 99999 !important;
    top: calc(100% + 2px) !important;
    left: 0 !important;
    transform: none !important;
    margin: 0 !important;
    min-width: 100px !important;
    width: 100px !important;
    background: rgba(0, 0, 0, 0.95) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 6px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4) !important;
}

/* 特殊处理右侧的下拉菜单，防止超出屏幕 */
.ql-toolbar .ql-picker:last-child .ql-picker-options,
.ql-toolbar .ql-picker:nth-last-child(2) .ql-picker-options,
.ql-toolbar .ql-picker:nth-last-child(3) .ql-picker-options {
    right: 0 !important;
    left: auto !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .ql-toolbar {
        padding: 6px !important;
        overflow-x: auto !important;
        overflow-y: visible !important;
        white-space: nowrap !important;
    }

    .ql-toolbar button {
        width: 24px !important;
        height: 24px !important;
        margin: 1px !important;
    }

    .ql-toolbar .ql-picker-label {
        min-width: 24px !important;
        height: 24px !important;
        padding: 2px 4px !important;
    }

    .ql-editor {
        font-size: 13px !important;
    }

    /* 移动端下拉菜单优化 */
    .ql-picker-options {
        max-width: calc(100vw - 40px) !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
    }
}

/* 最高优先级修复 - 强制覆盖所有Quill原生样式 */
.ql-snow .ql-picker-options,
.ql-snow .ql-color .ql-picker-options,
.ql-snow .ql-background .ql-picker-options,
.ql-snow .ql-align .ql-picker-options,
.ql-snow .ql-size .ql-picker-options,
.ql-snow .ql-header .ql-picker-options,
.ql-snow .ql-font .ql-picker-options {
    position: absolute !important;
    z-index: 99999 !important;
    top: calc(100% + 2px) !important;
    left: 0 !important;
    transform: none !important;
    margin: 0 !important;
    background: rgba(0, 0, 0, 0.95) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 6px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4) !important;
    max-height: 200px !important;
    overflow-y: auto !important;
    white-space: nowrap !important;
}

/* 强制颜色选择器在容器内 */
.ql-snow .ql-color .ql-picker-options,
.ql-snow .ql-background .ql-picker-options {
    width: 180px !important;
    max-width: 180px !important;
    padding: 8px !important;
}

/* 强制对齐选择器在容器内 */
.ql-snow .ql-align .ql-picker-options {
    width: 100px !important;
    max-width: 100px !important;
    min-width: 100px !important;
}

/* 强制所有容器允许溢出 */
.ql-snow,
.ql-snow .ql-toolbar,
.ql-snow .ql-container,
.quill-editor-container {
    overflow: visible !important;
}

/* 强制picker容器允许溢出 */
.ql-snow .ql-picker {
    overflow: visible !important;
    position: relative !important;
}

/* 防止下拉菜单被transform影响 */
.ql-snow .ql-picker-options[style*="transform"] {
    transform: none !important;
}

/* 防止下拉菜单被margin影响 */
.ql-snow .ql-picker-options[style*="margin"] {
    margin: 2px 0 0 0 !important;
}

/* 终极修复 - 强制所有下拉菜单在编辑器容器内 */
.quill-editor-container {
    position: relative !important;
    overflow: visible !important;
    contain: layout !important;
}

.quill-editor-container .ql-toolbar {
    position: relative !important;
    overflow: visible !important;
    contain: layout !important;
}

.quill-editor-container .ql-picker {
    position: relative !important;
    contain: layout !important;
}

/* 强制下拉菜单相对于工具栏定位 */
.quill-editor-container .ql-picker-options {
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    right: auto !important;
    bottom: auto !important;
    z-index: 1000 !important;
    transform: none !important;
    margin: 2px 0 0 0 !important;
    max-height: 150px !important;
    overflow-y: auto !important;
    background: rgba(0, 0, 0, 0.95) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 6px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4) !important;
}

/* 特殊处理颜色选择器 */
.quill-editor-container .ql-color .ql-picker-options,
.quill-editor-container .ql-background .ql-picker-options {
    width: 150px !important;
    max-width: 150px !important;
    min-width: 150px !important;
}

/* 特殊处理对齐选择器 */
.quill-editor-container .ql-align .ql-picker-options {
    width: 80px !important;
    max-width: 80px !important;
    min-width: 80px !important;
}

/* 如果右侧空间不足，向左对齐 */
.quill-editor-container .ql-picker:nth-last-child(-n+3) .ql-picker-options {
    left: auto !important;
    right: 0 !important;
}

/* 专门针对APP设置页面的修复 */
.app-settings-container,
.app-settings-content,
.tab-content,
.main-content {
    overflow: visible !important;
    overflow-x: visible !important;
    overflow-y: visible !important;
}

/* 强制所有可能的父容器允许溢出 */
body,
html,
.main-wrapper,
.main-content,
.app-settings-container,
.app-settings-content,
.tab-content,
.popup-content,
.agreement-content,
.update-content {
    overflow: visible !important;
    contain: none !important;
}

/* 超级强力修复 - 覆盖所有可能的限制 */
.quill-editor-container,
.quill-editor-container *,
.ql-toolbar,
.ql-toolbar *,
.ql-picker,
.ql-picker * {
    overflow: visible !important;
    contain: none !important;
    clip: none !important;
    clip-path: none !important;
}

/* 确保下拉菜单在最顶层 */
.ql-picker-options {
    position: fixed !important;
    z-index: 999999 !important;
}

/* 动态计算下拉菜单位置的JavaScript辅助类 */
.ql-picker-options.force-positioned {
    position: absolute !important;
    z-index: 999999 !important;
    top: 100% !important;
    left: 0 !important;
    transform: none !important;
    margin: 2px 0 0 0 !important;
}
