<?php
/**
 * 代码合并API - 处理UserScript代码合并请求
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 只接受POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => '只支持POST请求'], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    // 获取POST数据
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    // 如果JSON解析失败，尝试解析表单数据
    if (!$data) {
        $data = $_POST;
    }
    
    $mainScript = $data['main_script'] ?? '';
    $fragments = $data['fragments'] ?? [];
    
    // 验证输入
    if (empty($mainScript)) {
        throw new Exception('主脚本不能为空');
    }
    
    // 确保fragments是数组
    if (!is_array($fragments)) {
        $fragments = [];
    }
    
    // 执行代码合并
    $mergedScript = mergeUserScripts($mainScript, $fragments);
    
    echo json_encode([
        'success' => true,
        'result' => $mergedScript,
        'message' => '代码合并成功！'
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
} catch (Error $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => '服务器内部错误: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * UserScript代码合并核心函数
 */
function mergeUserScripts($mainScript, $fragments) {
    // 解析主脚本
    $mainHeader = extractHeader($mainScript);
    $mainBody = extractBody($mainScript);
    
    // 解析所有片段
    $fragmentHeaders = [];
    $fragmentBodies = [];
    
    foreach ($fragments as $fragment) {
        if (!empty(trim($fragment))) {
            $fragmentHeaders[] = extractHeader($fragment);
            $fragmentBodies[] = extractBody($fragment);
        }
    }
    
    // 合并头部
    $mergedHeader = mergeHeaders($mainHeader, $fragmentHeaders);
    
    // 合并代码体
    $mergedBody = mergeBodies($mainBody, $fragmentBodies);
    
    // 组装最终脚本 - 确保头部和代码体之间只有一个空行
    $finalScript = $mergedHeader . "\n\n" . $mergedBody;
    
    // 规范化最终脚本格式
    $finalScript = normalizeScriptFormat($finalScript);
    
    // 验证脚本
    validateScript($finalScript);
    
    return $finalScript;
}

/**
 * 提取UserScript头部
 */
function extractHeader($script) {
    if (preg_match('/\/\/ ==UserScript==(.*?)\/\/ ==\/UserScript==/s', $script, $matches)) {
        return '// ==UserScript==' . $matches[1] . '// ==/UserScript==';
    }
    return '';
}

/**
 * 提取UserScript代码体
 */
function extractBody($script) {
    if (preg_match('/\/\/ ==\/UserScript==\s*(.*)/s', $script, $matches)) {
        return trim($matches[1]);
    }
    // 如果没有头部，返回整个脚本
    return $script;
}

/**
 * 合并头部信息
 */
function mergeHeaders($mainHeader, $fragmentHeaders) {
    $uniqueHeaders = ['name', 'namespace', 'version', 'description', 'author'];
    $multiHeaders = ['include', 'match', 'exclude', 'require', 'resource', 'grant'];
    
    $merged = ['unique' => [], 'multi' => []];
    
    // 解析主脚本头部
    $mainParsed = parseHeader($mainHeader);
    foreach ($mainParsed as $item) {
        if (in_array($item['key'], $uniqueHeaders)) {
            $merged['unique'][$item['key']] = $item['value'];
        } else {
            if (!isset($merged['multi'][$item['key']])) {
                $merged['multi'][$item['key']] = [];
            }
            $merged['multi'][$item['key']][] = $item['value'];
        }
    }
    
    // 解析片段头部
    foreach ($fragmentHeaders as $header) {
        $parsed = parseHeader($header);
        foreach ($parsed as $item) {
            if (in_array($item['key'], $uniqueHeaders) && !isset($merged['unique'][$item['key']])) {
                $merged['unique'][$item['key']] = $item['value'];
            } elseif (in_array($item['key'], $multiHeaders)) {
                if (!isset($merged['multi'][$item['key']])) {
                    $merged['multi'][$item['key']] = [];
                }
                if (!in_array($item['value'], $merged['multi'][$item['key']])) {
                    $merged['multi'][$item['key']][] = $item['value'];
                }
            }
        }
    }
    
    // 生成最终头部 - 保持原有格式间距
    $headerText = "// ==UserScript==\n";
    foreach ($merged['unique'] as $key => $value) {
        $headerText .= "// @{$key}        {$value}\n";
    }
    foreach ($merged['multi'] as $key => $values) {
        foreach ($values as $value) {
            $headerText .= "// @{$key}        {$value}\n";
        }
    }
    $headerText .= "// ==/UserScript==";
    
    return $headerText;
}

/**
 * 解析头部信息
 */
function parseHeader($header) {
    $items = [];
    if (preg_match_all('/\/\/ @(\w+)\s+(.+)/m', $header, $matches, PREG_SET_ORDER)) {
        foreach ($matches as $match) {
            $items[] = ['key' => $match[1], 'value' => trim($match[2])];
        }
    }
    return $items;
}

/**
 * 合并代码体 - 智能插入逻辑
 */
function mergeBodies($mainBody, $fragmentBodies) {
    $mergedBody = $mainBody;
    
    foreach ($fragmentBodies as $body) {
        if (!empty(trim($body))) {
            $cleanedBody = cleanFragmentCode($body);
            $mergedBody = smartInsertCode($mergedBody, $cleanedBody);
        }
    }
    
    return $mergedBody;
}

/**
 * 清理片段代码 - 移除片段中的UserScript头部和多余的IIFE包装
 */
function cleanFragmentCode($fragmentCode) {
    // 移除UserScript头部（如果存在）
    $cleanedCode = preg_replace('/\/\/ ==UserScript==.*?\/\/ ==\/UserScript==\s*/s', '', $fragmentCode);
    
    // 检查是否是完整的IIFE包装
    if (preg_match('/^\s*\(function\(\)\s*\{\s*[\'"]use strict[\'"];\s*(.*)\s*\}\)\(\);\s*$/s', $cleanedCode, $matches)) {
        // 提取IIFE内部的代码
        $cleanedCode = trim($matches[1]);
    } elseif (preg_match('/^\s*\(function\(\)\s*\{\s*(.*)\s*\}\)\(\);\s*$/s', $cleanedCode, $matches)) {
        // 提取IIFE内部的代码（无use strict）
        $cleanedCode = trim($matches[1]);
    }
    
    // 规范化空行 - 移除多余的连续空行
    $cleanedCode = preg_replace('/\n\s*\n\s*\n+/', "\n\n", $cleanedCode);
    
    return trim($cleanedCode);
}

/**
 * 智能代码插入 - 根据主代码结构选择最佳插入位置
 */
function smartInsertCode($mainBody, $fragmentCode) {
    // 1. 检查主代码是否是IIFE结构
    if (preg_match('/^\s*\(function\(\)\s*\{\s*/', $mainBody)) {
        return insertIntoIIFE($mainBody, $fragmentCode);
    }
    
    // 2. 检查是否是模块化代码（有导入导出）
    if (preg_match('/(import\s+.*from|export\s+|module\.exports|require\s*\()/', $mainBody)) {
        return insertIntoModule($mainBody, $fragmentCode);
    }
    
    // 3. 检查是否有jQuery ready或DOMContentLoaded
    if (preg_match('/(\$\(document\)\.ready|\$\(function|\bDOMContentLoaded\b)/', $mainBody)) {
        return insertIntoDOMReady($mainBody, $fragmentCode);
    }
    
    // 4. 检查主代码是否有明确的函数或类定义
    if (preg_match('/(function\s+\w+|class\s+\w+|const\s+\w+\s*=|let\s+\w+\s*=|var\s+\w+\s*=)/', $mainBody)) {
        return insertAtOptimalPosition($mainBody, $fragmentCode);
    }
    
    // 5. 默认：在代码末尾插入
    return $mainBody . "\n\n// === 合并的代码片段 ===\n" . $fragmentCode . "\n// === 片段结束 ===";
}

/**
 * 在模块化代码中插入片段
 */
function insertIntoModule($mainBody, $fragmentCode) {
    // 在import语句后，export语句前插入
    if (preg_match('/(.*?)((?:import\s+.*?;\s*)+)(.*)/s', $mainBody, $matches)) {
        return $matches[1] . $matches[2] . "\n// === 合并的代码片段 ===\n" . $fragmentCode . "\n// === 片段结束 ===\n\n" . $matches[3];
    }
    
    // 默认在开头插入
    return "// === 合并的代码片段 ===\n" . $fragmentCode . "\n// === 片段结束 ===\n\n" . $mainBody;
}

/**
 * 在DOM ready函数中插入片段
 */
function insertIntoDOMReady($mainBody, $fragmentCode) {
    // jQuery $(document).ready 或 $(function() {})
    if (preg_match('/(\$\(document\)\.ready\s*\(\s*function\s*\(\s*\)\s*\{|\$\(function\s*\(\s*\)\s*\{)/', $mainBody)) {
        return preg_replace('/(\$\((?:document\)\.ready\s*\(\s*function\s*\(\s*\)\s*\{|\s*function\s*\(\s*\)\s*\{))/', 
                          '$1' . "\n    // === 合并的代码片段 ===\n    " . indentCode($fragmentCode, 4) . "\n    // === 片段结束 ===\n", 
                          $mainBody, 1);
    }
    
    // DOMContentLoaded
    if (preg_match('/(document\.addEventListener\s*\(\s*[\'"]DOMContentLoaded[\'"],\s*function\s*\(\s*\)\s*\{)/', $mainBody)) {
        return preg_replace('/(document\.addEventListener\s*\(\s*[\'"]DOMContentLoaded[\'"],\s*function\s*\(\s*\)\s*\{)/', 
                          '$1' . "\n    // === 合并的代码片段 ===\n    " . indentCode($fragmentCode, 4) . "\n    // === 片段结束 ===\n", 
                          $mainBody, 1);
    }
    
    return insertAtOptimalPosition($mainBody, $fragmentCode);
}

/**
 * 在最优位置插入代码
 */
function insertAtOptimalPosition($mainBody, $fragmentCode) {
    $lines = explode("\n", $mainBody);
    $insertPosition = findOptimalInsertPosition($lines);
    
    // 在找到的位置插入代码
    $beforeLines = array_slice($lines, 0, $insertPosition);
    $afterLines = array_slice($lines, $insertPosition);
    
    $fragmentLines = [
        "// === 合并的代码片段 ===",
        ...explode("\n", $fragmentCode),
        "// === 片段结束 ==="
    ];
    
    // 智能处理空行
    $result = [];
    
    // 添加前置代码
    if (!empty($beforeLines)) {
        $result = array_merge($result, $beforeLines);
        $result[] = ""; // 添加一个空行分隔
    }
    
    // 添加片段代码
    $result = array_merge($result, $fragmentLines);
    
    // 添加后置代码
    if (!empty($afterLines)) {
        $result[] = ""; // 添加一个空行分隔
        $result = array_merge($result, $afterLines);
    }
    
    return implode("\n", $result);
}

/**
 * 查找最优插入位置
 */
function findOptimalInsertPosition($lines) {
    $variableDeclarations = [];
    $functionDeclarations = [];
    $mainLogic = [];
    
    for ($i = 0; $i < count($lines); $i++) {
        $line = trim($lines[$i]);
        
        // 跳过空行和注释
        if (empty($line) || preg_match('/^\/\/|^\/\*|\*\//', $line)) {
            continue;
        }
        
        // 检测变量声明
        if (preg_match('/^(const|let|var)\s+\w+/', $line)) {
            $variableDeclarations[] = $i;
        }
        // 检测函数声明
        elseif (preg_match('/^function\s+\w+|^\w+\s*=\s*function|^\w+\s*=\s*\(.*\)\s*=>/', $line)) {
            $functionDeclarations[] = $i;
        }
        // 其他逻辑代码
        else {
            $mainLogic[] = $i;
        }
    }
    
    // 优先在变量声明后、函数声明前插入
    if (!empty($variableDeclarations) && !empty($functionDeclarations)) {
        return max($variableDeclarations) + 1;
    }
    
    // 如果只有变量声明，在变量声明后插入
    if (!empty($variableDeclarations)) {
        return max($variableDeclarations) + 1;
    }
    
    // 如果只有函数声明，在函数声明前插入
    if (!empty($functionDeclarations)) {
        return min($functionDeclarations);
    }
    
    // 如果有主逻辑，在主逻辑前插入
    if (!empty($mainLogic)) {
        return min($mainLogic);
    }
    
    // 默认在末尾插入
    return count($lines);
}

/**
 * 在IIFE函数内部的合适位置插入代码
 */
function insertIntoIIFE($mainBody, $fragmentCode) {
    // 查找IIFE函数的结构
    if (preg_match('/^(\s*\(function\(\)\s*\{\s*)(.*?)(\s*\}\)\(\);\s*)$/s', $mainBody, $matches)) {
        $prefix = $matches[1];
        $innerCode = $matches[2];
        $suffix = $matches[3];
        
        // 检查是否有'use strict'声明
        if (preg_match('/^(\s*[\'"]use strict[\'"];\s*)(.*?)$/s', $innerCode, $strictMatches)) {
            $strictDeclaration = $strictMatches[1];
            $actualCode = trim($strictMatches[2]);
            
            // 分析内部代码结构，找到最佳插入位置
            $innerLines = explode("\n", $actualCode);
            $insertPos = findOptimalInsertPosition($innerLines);
            
            $beforeInner = array_slice($innerLines, 0, $insertPos);
            $afterInner = array_slice($innerLines, $insertPos);
            
            $fragmentLines = [
                "    // === 合并的代码片段 ===",
                ...explode("\n", indentCode($fragmentCode, 4)),
                "    // === 片段结束 ==="
            ];
            
            // 智能处理空行 - 确保合适的间距
            $mergedInnerLines = [$strictDeclaration];
            
            // 添加前置代码
            if (!empty($beforeInner)) {
                $mergedInnerLines = array_merge($mergedInnerLines, $beforeInner);
                $mergedInnerLines[] = ""; // 添加一个空行分隔
            }
            
            // 添加片段代码
            $mergedInnerLines = array_merge($mergedInnerLines, $fragmentLines);
            
            // 添加后置代码
            if (!empty($afterInner)) {
                $mergedInnerLines[] = ""; // 添加一个空行分隔
                $mergedInnerLines = array_merge($mergedInnerLines, $afterInner);
            }
            
            $mergedInner = implode("\n", $mergedInnerLines);
        } else {
            // 没有use strict，直接分析代码结构
            $innerLines = explode("\n", trim($innerCode));
            $insertPos = findOptimalInsertPosition($innerLines);
            
            $beforeInner = array_slice($innerLines, 0, $insertPos);
            $afterInner = array_slice($innerLines, $insertPos);
            
            $fragmentLines = [
                "    // === 合并的代码片段 ===",
                ...explode("\n", indentCode($fragmentCode, 4)),
                "    // === 片段结束 ==="
            ];
            
            // 智能处理空行 - 确保合适的间距
            $mergedInnerLines = [];
            
            // 添加前置代码
            if (!empty($beforeInner)) {
                $mergedInnerLines = array_merge($mergedInnerLines, $beforeInner);
                $mergedInnerLines[] = ""; // 添加一个空行分隔
            }
            
            // 添加片段代码
            $mergedInnerLines = array_merge($mergedInnerLines, $fragmentLines);
            
            // 添加后置代码
            if (!empty($afterInner)) {
                $mergedInnerLines[] = ""; // 添加一个空行分隔
                $mergedInnerLines = array_merge($mergedInnerLines, $afterInner);
            }
            
            $mergedInner = implode("\n", $mergedInnerLines);
        }
        
        return $prefix . $mergedInner . $suffix;
    }
    
    // 如果无法解析IIFE结构，使用默认方式
    return $mainBody . "\n\n// === 合并的代码片段 ===\n" . $fragmentCode . "\n// === 片段结束 ===";
}

/**
 * 代码缩进处理
 */
function indentCode($code, $spaces) {
    $indent = str_repeat(' ', $spaces);
    return $indent . str_replace("\n", "\n" . $indent, trim($code));
}

/**
 * 规范化脚本格式
 */
function normalizeScriptFormat($script) {
    // 1. 移除多余的连续空行（超过2个的空行）
    $script = preg_replace('/\n\s*\n\s*\n+/', "\n\n", $script);
    
    // 2. 确保IIFE结构内部的格式正确
    $script = preg_replace('/(\(function\(\)\s*\{\s*[\'"]use strict[\'"];\s*)\n+/', '$1' . "\n\n", $script);
    
    // 3. 移除文件末尾多余的空行
    $script = rtrim($script) . "\n";
    
    // 4. 确保代码片段标记后有适当的空行
    $script = preg_replace('/(\/\/ === 合并的代码片段 ===)\n+/', '$1' . "\n", $script);
    $script = preg_replace('/(\/\/ === 片段结束 ===)\n+/', '$1' . "\n", $script);
    
    return $script;
}

/**
 * 验证脚本
 */
function validateScript($script) {
    if (strpos($script, '// ==UserScript==') === false) {
        throw new Exception('脚本头部缺失');
    }
    
    if (strpos($script, '// ==/UserScript==') === false) {
        throw new Exception('脚本头部结束标记缺失');
    }
    
    // 检查潜在的安全问题
    $forbidden = ['document.cookie', 'localStorage.clear', 'eval('];
    foreach ($forbidden as $item) {
        if (strpos($script, $item) !== false) {
            throw new Exception("检测到潜在安全风险: {$item}");
        }
    }
    
    return true;
}
?> 