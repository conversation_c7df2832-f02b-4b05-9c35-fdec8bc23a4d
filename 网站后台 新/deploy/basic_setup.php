<?php
/**
 * 基本模式数据库表初始化
 * 用于在安全配置失败时创建必要的数据库表
 */

// 防止直接访问
if (!defined('API_SECURITY_INIT')) {
    define('API_SECURITY_INIT', true);
}

function initializeBasicTables($pdo) {
    try {
        // 创建API密钥表
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS api_keys (
                id INT AUTO_INCREMENT PRIMARY KEY,
                key_name VARCHAR(255) NOT NULL,
                api_key VARCHAR(255) NOT NULL UNIQUE,
                is_active BOOLEAN DEFAULT TRUE,
                created_by INT DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_api_key (api_key),
                INDEX idx_active (is_active)
            )
        ");
        
        // 创建令牌管理表
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS token_management (
                id INT AUTO_INCREMENT PRIMARY KEY,
                token_type ENUM('api_key', 'jwt_secret') NOT NULL,
                token_value VARCHAR(512) NOT NULL,
                expires_at TIMESTAMP NULL,
                is_active BOOLEAN DEFAULT TRUE,
                auto_refresh BOOLEAN DEFAULT TRUE,
                refresh_interval INT DEFAULT 86400,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_type_active (token_type, is_active),
                INDEX idx_expires (expires_at)
            )
        ");
        
        // 创建令牌同步日志表
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS token_sync_log (
                id INT AUTO_INCREMENT PRIMARY KEY,
                token_id INT NOT NULL,
                sync_type ENUM('generate', 'refresh', 'revoke', 'sync') NOT NULL,
                target_files TEXT,
                status ENUM('pending', 'success', 'failed') DEFAULT 'pending',
                error_message TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_token_id (token_id),
                INDEX idx_status (status)
            )
        ");
        
        return true;
        
    } catch (Exception $e) {
        error_log("Basic tables initialization failed: " . $e->getMessage());
        return false;
    }
}

/**
 * 生成基本的API密钥
 */
function generateBasicApiKey($pdo, $keyName = 'Basic API Key') {
    try {
        $keyValue = bin2hex(random_bytes(32));
        $keyHash = hash('sha256', $keyValue);
        
        $stmt = $pdo->prepare("
            INSERT INTO api_keys (key_name, api_key, is_active, created_by, created_at) 
            VALUES (?, ?, 1, 1, NOW())
        ");
        $stmt->execute([$keyName, $keyHash]);
        
        return [
            'id' => $pdo->lastInsertId(),
            'key_value' => $keyValue,
            'key_hash' => $keyHash
        ];
        
    } catch (Exception $e) {
        error_log("Basic API key generation failed: " . $e->getMessage());
        return false;
    }
}

/**
 * 检查并初始化基本配置
 */
function checkAndInitializeBasic($pdo) {
    $initialized = false;
    
    try {
        // 检查是否已有API密钥
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM api_keys WHERE is_active = 1");
        $stmt->execute();
        $result = $stmt->fetch();
        
        if ($result['count'] == 0) {
            // 生成第一个API密钥
            $apiKey = generateBasicApiKey($pdo, '默认API密钥');
            if ($apiKey) {
                $initialized = true;
            }
        }
        
    } catch (Exception $e) {
        // 表可能不存在，尝试创建
        if (initializeBasicTables($pdo)) {
            $apiKey = generateBasicApiKey($pdo, '默认API密钥');
            if ($apiKey) {
                $initialized = true;
            }
        }
    }
    
    return $initialized;
} 