<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小梅花智能AI客服系统 - 后台管理</title>
    <link rel="stylesheet" href="../assets/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- 防止表单重新提交弹窗的全局解决方案 -->
    <script src="../assets/form-resubmission-prevention.js?v=<?php echo time(); ?>"></script>
</head>
<body>
    <header class="main-header">
        <div class="header-left">
            <button class="sidebar-toggle" onclick="toggleSidebar()">
                <i class="fas fa-bars"></i>
            </button>
            <h1 class="header-title">
                <i class="fas fa-robot"></i>
                小梅花AI客服系统
            </h1>
        </div>
        
        <div class="header-right">
            <div class="header-user">
                <div class="user-info">
                    <span class="user-name"><?php echo htmlspecialchars($admin_user['username']); ?></span>
                    <small class="user-role">管理员</small>
                </div>
                <div class="user-avatar">
                    <i class="fas fa-user-circle"></i>
                </div>
                <div class="user-dropdown">
                    <a href="?page=settings" class="dropdown-item">
                        <i class="fas fa-cog"></i> 系统设置
                    </a>
                    <a href="logout.php" class="dropdown-item">
                        <i class="fas fa-sign-out-alt"></i> 退出登录
                    </a>
                </div>
            </div>
        </div>
    </header>

    <div class="container"> 

<style>
.main-header {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 0 30px;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
}

.header-left {
    display: flex;
    align-items: center;
    gap: 20px;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.8);
    font-size: 18px;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.sidebar-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.header-title {
    color: white;
    font-size: 20px;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.header-user {
    position: relative;
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.header-user:hover {
    background: rgba(255, 255, 255, 0.1);
}

.user-info {
    text-align: right;
}

.user-name {
    color: white;
    font-weight: 500;
    display: block;
}

.user-role {
    color: rgba(255, 255, 255, 0.7);
    font-size: 12px;
}

.user-avatar {
    color: rgba(255, 255, 255, 0.8);
    font-size: 24px;
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    min-width: 180px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1000;
}

.header-user:hover .user-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 16px;
    color: #333;
    text-decoration: none;
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.dropdown-item:last-child {
    border-bottom: none;
}

.dropdown-item:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

@media (max-width: 768px) {
    .main-header {
        padding: 0 20px;
    }
    
    .header-title {
        font-size: 18px;
    }
    
    .user-info {
        display: none;
    }
}
</style> 