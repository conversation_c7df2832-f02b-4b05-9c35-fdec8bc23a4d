<?php
// 获取用户数据（基于卡密使用情况）
$search = $_GET['search'] ?? '';
$page_num = max(1, intval($_GET['p'] ?? 1));
$per_page = 20;
$offset = ($page_num - 1) * $per_page;

$where_clause = "";
$params = [];
if (!empty($search)) {
    $where_clause = "WHERE lk.license_key LIKE ? OR lk.key_value LIKE ? OR lk.store_name LIKE ? OR lk.last_used_ip LIKE ?";
    $search_param = "%{$search}%";
    $params = [$search_param, $search_param, $search_param, $search_param];
}

// 获取用户列表（活跃用户）
$users_sql = "
    SELECT lk.*, s.name as script_name, s.version as script_version,
           CASE 
               WHEN lk.last_heartbeat >= DATE_SUB(NOW(), INTERVAL 5 MINUTE) THEN 'online'
               WHEN lk.last_heartbeat >= DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 'recently'
               ELSE 'offline'
           END as online_status
    FROM license_keys lk 
    LEFT JOIN scripts s ON lk.script_id = s.id 
    {$where_clause}
    ORDER BY lk.last_heartbeat DESC 
    LIMIT {$per_page} OFFSET {$offset}
";
$users_stmt = $pdo->prepare($users_sql);
$users_stmt->execute($params);
$users = $users_stmt->fetchAll(PDO::FETCH_ASSOC);

// 获取总数
$count_sql = "SELECT COUNT(*) FROM license_keys lk " . $where_clause;
$count_stmt = $pdo->prepare($count_sql);
$count_stmt->execute($params);
$total_users = $count_stmt->fetchColumn();
$total_pages = ceil($total_users / $per_page);

// 统计数据
$online_count = $pdo->query("SELECT COUNT(*) FROM license_keys WHERE last_heartbeat >= DATE_SUB(NOW(), INTERVAL 5 MINUTE)")->fetchColumn();
$recently_count = $pdo->query("SELECT COUNT(*) FROM license_keys WHERE last_heartbeat >= DATE_SUB(NOW(), INTERVAL 1 HOUR) AND last_heartbeat < DATE_SUB(NOW(), INTERVAL 5 MINUTE)")->fetchColumn();
$total_registered = $pdo->query("SELECT COUNT(*) FROM license_keys WHERE last_heartbeat IS NOT NULL")->fetchColumn();
?>

<style>
/* 用户管理页面专用样式 - 与脚本管理保持一致 */
.users-container {
    max-width: 100%;
    margin: 0;
    padding: 20px;
    box-sizing: border-box;
}

.card {
    background: rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(20px) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 15px !important;
    padding: 20px !important;
    margin-bottom: 20px !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
}

.card h2 {
    color: white !important;
    margin-bottom: 20px !important;
    font-size: 18px !important;
    font-weight: 600 !important;
    display: flex !important;
    align-items: center !important;
    gap: 10px !important;
}

table {
    width: 100% !important;
    border-collapse: collapse !important;
    font-size: 13px !important;
}

table th {
    background: rgba(255, 255, 255, 0.1) !important;
    color: white !important;
    padding: 15px 12px !important;
    text-align: left !important;
    font-weight: 600 !important;
    font-size: 14px !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
}

table td {
    padding: 15px 12px !important;
    color: rgba(255, 255, 255, 0.9) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
    vertical-align: middle !important;
}

.status-badge {
    display: inline-block !important;
    padding: 6px 12px !important;
    border-radius: 12px !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    background: rgba(255, 255, 255, 0.2) !important;
    color: white !important;
}

.stats-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
    gap: 20px !important;
    margin-bottom: 30px !important;
}

.stat-item {
    background: rgba(255, 255, 255, 0.2) !important;
    backdrop-filter: blur(15px) !important;
    padding: 25px !important;
    border-radius: 15px !important;
    text-align: center !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    transition: all 0.3s ease !important;
}

.stat-item .value {
    font-size: 36px !important;
    font-weight: 700 !important;
    color: white !important;
    margin-bottom: 8px !important;
}

.stat-item .label {
    font-size: 14px !important;
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500 !important;
}
</style>

<div class="users-container">
    <div class="card">
        <h2><i class="fas fa-users"></i> 用户概览</h2>
        <div class="stats-grid">
            <div class="stat-item">
                <div class="value"><?php echo $online_count; ?></div>
                <div class="label"><i class="fas fa-circle" style="color: #28a745;"></i> 在线用户</div>
            </div>
            <div class="stat-item">
                <div class="value"><?php echo $recently_count; ?></div>
                <div class="label"><i class="fas fa-circle" style="color: #ffc107;"></i> 最近活跃</div>
            </div>
            <div class="stat-item">
                <div class="value"><?php echo $total_registered; ?></div>
                <div class="label"><i class="fas fa-user-check"></i> 注册用户</div>
            </div>
            <div class="stat-item">
                <div class="value"><?php echo $total_users; ?></div>
                <div class="label"><i class="fas fa-users"></i> 总用户数</div>
            </div>
        </div>
    </div>

    <div class="card">
        <h2><i class="fas fa-list"></i> 用户列表</h2>
        
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; flex-wrap: wrap; gap: 15px;">
            <form method="GET" style="display: flex; gap: 10px; align-items: center;">
                <input type="hidden" name="page" value="users">
                <input type="text" name="search" value="<?php echo htmlspecialchars($search); ?>" placeholder="搜索卡密/店铺/IP..." style="width: 250px;">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i> 搜索
                </button>
                <?php if ($search): ?>
                    <a href="index.php?page=users" class="btn btn-secondary">
                        <i class="fas fa-times"></i> 清除
                    </a>
                <?php endif; ?>
            </form>
            
            <div style="color: rgba(255,255,255,0.8); font-size: 14px;">
                <i class="fas fa-info-circle"></i> 
                实时状态：
                <span style="color: #28a745;">●</span> 在线 
                <span style="color: #ffc107;">●</span> 最近活跃 
                <span style="color: #6c757d;">●</span> 离线
            </div>
        </div>
        
        <?php if (empty($users)): ?>
            <p style="color: rgba(255,255,255,0.7); text-align: center; padding: 40px;">
                <i class="fas fa-info-circle"></i> 
                <?php echo $search ? '没有找到匹配的用户' : '暂无用户数据'; ?>
            </p>
        <?php else: ?>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th><i class="fas fa-signal"></i> 状态</th>
                            <th><i class="fas fa-key"></i> 卡密</th>
                            <th><i class="fas fa-store"></i> 店铺信息</th>
                            <th><i class="fas fa-code"></i> 使用脚本</th>
                            <th><i class="fas fa-globe"></i> IP地址</th>
                            <th><i class="fas fa-clock"></i> 最后活动</th>
                            <th><i class="fas fa-calendar"></i> 到期时间</th>
                            <th><i class="fas fa-tag"></i> 卡密类型</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($users as $user): ?>
                        <tr>
                            <td>
                                <?php
                                $status_colors = [
                                    'online' => '#28a745',
                                    'recently' => '#ffc107', 
                                    'offline' => '#6c757d'
                                ];
                                $status_texts = [
                                    'online' => '在线',
                                    'recently' => '最近活跃',
                                    'offline' => '离线'
                                ];
                                $color = $status_colors[$user['online_status']];
                                $text = $status_texts[$user['online_status']];
                                ?>
                                <span style="display: flex; align-items: center; gap: 8px;">
                                    <i class="fas fa-circle" style="color: <?php echo $color; ?>; font-size: 10px;"></i>
                                    <span style="color: <?php echo $color; ?>; font-weight: 600;"><?php echo $text; ?></span>
                                </span>
                            </td>
                            <td style="font-family: monospace; font-size: 12px;">
                                <?php echo htmlspecialchars(substr($user['license_key'] ?? $user['key_value'], 0, 15) . '...'); ?>
                            </td>
                            <td>
                                <?php if ($user['store_name'] || $user['wechat_store_id']): ?>
                                    <?php if ($user['store_name']): ?>
                                        <strong><?php echo htmlspecialchars($user['store_name']); ?></strong><br>
                                    <?php endif; ?>
                                    <?php if ($user['wechat_store_id']): ?>
                                        <small>ID: <?php echo htmlspecialchars($user['wechat_store_id']); ?></small>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <span style="opacity: 0.5;">未绑定</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($user['script_name']): ?>
                                    <strong><?php echo htmlspecialchars($user['script_name']); ?></strong><br>
                                    <small style="opacity: 0.7;">v<?php echo htmlspecialchars($user['script_version']); ?></small>
                                <?php else: ?>
                                    <span style="opacity: 0.5;">未绑定</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($user['last_used_ip']): ?>
                                    <span style="font-family: monospace;"><?php echo htmlspecialchars($user['last_used_ip']); ?></span>
                                <?php else: ?>
                                    <span style="opacity: 0.5;">未知</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($user['last_heartbeat']): ?>
                                    <?php echo format_time_ago($user['last_heartbeat']); ?>
                                    <br><small style="opacity: 0.7;"><?php echo date('m-d H:i', strtotime($user['last_heartbeat'])); ?></small>
                                <?php else: ?>
                                    <span style="opacity: 0.5;">从未使用</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php
                                $now = new DateTime();
                                $expiry = new DateTime($user['expiry_date']);
                                $is_expired = $now > $expiry;
                                ?>
                                <?php echo date('m-d H:i', strtotime($user['expiry_date'])); ?>
                                <?php if ($is_expired): ?>
                                    <br><small style="color: #ff6b6b;">已过期</small>
                                <?php else: ?>
                                    <?php
                                    $diff = $expiry->diff($now);
                                    if ($diff->days > 0) {
                                        echo "<br><small style='color: #4ecdc4;'>还有{$diff->days}天</small>";
                                    } elseif ($diff->h > 0) {
                                        echo "<br><small style='color: #ffa726;'>还有{$diff->h}小时</small>";
                                    } else {
                                        echo "<br><small style='color: #ff6b6b;'>即将过期</small>";
                                    }
                                    ?>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php
                                $type_names = [
                                    'yearly' => '年卡',
                                    'half_yearly' => '半年卡',
                                    'monthly' => '月卡',
                                    'daily' => '天卡',
                                    'hourly' => '小时卡'
                                ];
                                $type_colors = [
                                    'yearly' => '#28a745',
                                    'half_yearly' => '#17a2b8',
                                    'monthly' => '#007bff',
                                    'daily' => '#ffc107',
                                    'hourly' => '#fd7e14'
                                ];
                                $type_name = $type_names[$user['type']] ?? $user['type'];
                                $type_color = $type_colors[$user['type']] ?? '#6c757d';
                                ?>
                                <span class="status-badge" style="background: rgba(<?php 
                                    echo implode(',', sscanf($type_color, '#%02x%02x%02x')); 
                                ?>, 0.2); color: <?php echo $type_color; ?>; border-color: <?php echo $type_color; ?>;">
                                    <?php echo $type_name; ?>
                                </span>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <?php if ($total_pages > 1): ?>
            <div style="margin-top: 20px; text-align: center;">
                <div style="display: inline-flex; gap: 5px; align-items: center;">
                    <?php if ($page_num > 1): ?>
                        <a href="index.php?page=users&p=<?php echo $page_num - 1; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>" class="btn btn-secondary">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    <?php endif; ?>
                    
                    <span style="color: rgba(255,255,255,0.8); margin: 0 15px;">
                        第 <?php echo $page_num; ?> 页，共 <?php echo $total_pages; ?> 页 (<?php echo $total_users; ?> 个用户)
                    </span>
                    
                    <?php if ($page_num < $total_pages): ?>
                        <a href="index.php?page=users&p=<?php echo $page_num + 1; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>" class="btn btn-secondary">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>

    <div class="card">
        <h2><i class="fas fa-chart-bar"></i> 用户活跃度分析</h2>
        
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
            <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 15px;">
                <h4 style="color: white; margin-bottom: 15px;">
                    <i class="fas fa-clock"></i> 今日活跃用户
                </h4>
                <?php
                $today_active = $pdo->query("SELECT COUNT(*) FROM license_keys WHERE DATE(last_heartbeat) = CURDATE()")->fetchColumn();
                ?>
                <div style="font-size: 2rem; font-weight: bold; color: #4ecdc4; margin-bottom: 10px;">
                    <?php echo $today_active; ?>
                </div>
                <p style="color: rgba(255,255,255,0.7);">今天有活动的用户数量</p>
            </div>
            
            <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 15px;">
                <h4 style="color: white; margin-bottom: 15px;">
                    <i class="fas fa-calendar-week"></i> 本周活跃用户
                </h4>
                <?php
                $week_active = $pdo->query("SELECT COUNT(*) FROM license_keys WHERE last_heartbeat >= DATE_SUB(NOW(), INTERVAL 7 DAY)")->fetchColumn();
                ?>
                <div style="font-size: 2rem; font-weight: bold; color: #28a745; margin-bottom: 10px;">
                    <?php echo $week_active; ?>
                </div>
                <p style="color: rgba(255,255,255,0.7);">最近7天有活动的用户</p>
            </div>
            
            <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 15px;">
                <h4 style="color: white; margin-bottom: 15px;">
                    <i class="fas fa-percentage"></i> 活跃率
                </h4>
                <?php
                $total_valid = $pdo->query("SELECT COUNT(*) FROM license_keys WHERE status = 'active' AND expiry_date > NOW()")->fetchColumn();
                $active_rate = $total_valid > 0 ? round(($week_active / $total_valid) * 100, 1) : 0;
                ?>
                <div style="font-size: 2rem; font-weight: bold; color: #ffc107; margin-bottom: 10px;">
                    <?php echo $active_rate; ?>%
                </div>
                <p style="color: rgba(255,255,255,0.7);">有效用户中的活跃比例</p>
            </div>
        </div>
    </div> 
</div> 