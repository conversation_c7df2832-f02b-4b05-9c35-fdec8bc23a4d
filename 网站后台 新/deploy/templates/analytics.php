<?php
// 获取统计数据
try {
    $total_keys = $pdo->query("SELECT COUNT(*) FROM license_keys")->fetchColumn();
    $active_keys = $pdo->query("SELECT COUNT(*) FROM license_keys WHERE status = 'active'")->fetchColumn();
    $expired_keys = $pdo->query("SELECT COUNT(*) FROM license_keys WHERE expiry_date <= datetime('now')")->fetchColumn();
    $total_scripts = $pdo->query("SELECT COUNT(*) FROM scripts")->fetchColumn();
    
    // 今日数据
    $today_keys = $pdo->query("SELECT COUNT(*) FROM license_keys WHERE DATE(created_at) = DATE('now')")->fetchColumn();
    $today_active = $pdo->query("SELECT COUNT(*) FROM license_keys WHERE DATE(last_heartbeat) = DATE('now')")->fetchColumn();
    
    // 近7天数据
    $week_keys = $pdo->query("SELECT COUNT(*) FROM license_keys WHERE created_at >= datetime('now', '-7 days')")->fetchColumn();
    $week_active = $pdo->query("SELECT COUNT(*) FROM license_keys WHERE last_heartbeat >= datetime('now', '-7 days')")->fetchColumn();
    
} catch (Exception $e) {
    $total_keys = $active_keys = $expired_keys = $total_scripts = 0;
    $today_keys = $today_active = $week_keys = $week_active = 0;
}

// 获取每日新增数据（最近7天）
$daily_data = [];
for ($i = 6; $i >= 0; $i--) {
    $date = date('Y-m-d', strtotime("-{$i} days"));
    $daily_new = $pdo->prepare("SELECT COUNT(*) FROM license_keys WHERE DATE(created_at) = ?");
    $daily_new->execute([$date]);
    $daily_active = $pdo->prepare("SELECT COUNT(*) FROM license_keys WHERE DATE(last_heartbeat) = ?");
    $daily_active->execute([$date]);
    
    $daily_data[] = [
        'date' => $date,
        'new' => $daily_new->fetchColumn(),
        'active' => $daily_active->fetchColumn()
    ];
}

// 获取卡密类型分布
$type_distribution = [];
$types = ['yearly', 'monthly', 'weekly', 'daily'];
foreach ($types as $type) {
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM license_keys WHERE type = ?");
    $stmt->execute([$type]);
    $count = $stmt->fetchColumn();
    
    $active_stmt = $pdo->prepare("SELECT COUNT(*) FROM license_keys WHERE type = ? AND status = 'active'");
    $active_stmt->execute([$type]);
    $active_count = $active_stmt->fetchColumn();
    
    $type_distribution[] = [
        'type' => $type,
        'total' => $count,
        'active' => $active_count
    ];
}
?>

<style>
/* 数据分析页面专用样式 - 与脚本管理保持一致 */
.analytics-container {
    max-width: 100%;
    margin: 0;
    padding: 20px;
    box-sizing: border-box;
}

.card {
    background: rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(20px) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 15px !important;
    padding: 20px !important;
    margin-bottom: 20px !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
}

.card h2 {
    color: white !important;
    margin-bottom: 20px !important;
    font-size: 18px !important;
    font-weight: 600 !important;
    display: flex !important;
    align-items: center !important;
    gap: 10px !important;
}

.stats-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
    gap: 20px !important;
    margin-bottom: 30px !important;
}

.stat-item {
    background: rgba(255, 255, 255, 0.2) !important;
    backdrop-filter: blur(15px) !important;
    padding: 25px !important;
    border-radius: 15px !important;
    text-align: center !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    transition: all 0.3s ease !important;
}

.stat-item:hover {
    transform: translateY(-5px) !important;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2) !important;
}

.stat-item .value {
    font-size: 36px !important;
    font-weight: 700 !important;
    color: white !important;
    margin-bottom: 8px !important;
}

.stat-item .label {
    font-size: 14px !important;
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500 !important;
}

.chart-container {
    background: rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(20px) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 15px !important;
    padding: 20px !important;
    margin-bottom: 20px !important;
    height: 300px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}
</style>

<div class="analytics-container">
    <div class="card">
        <h2><i class="fas fa-chart-line"></i> 数据概览</h2>
        
        <div class="stats-grid">
            <div class="stat-item">
                <div class="value"><?php echo number_format($total_keys); ?></div>
                <div class="label">总卡密数</div>
            </div>
            <div class="stat-item">
                <div class="value"><?php echo number_format($active_keys); ?></div>
                <div class="label">有效卡密</div>
            </div>
            <div class="stat-item">
                <div class="value"><?php echo number_format($expired_keys); ?></div>
                <div class="label">过期卡密</div>
            </div>
            <div class="stat-item">
                <div class="value"><?php echo number_format($total_scripts); ?></div>
                <div class="label">脚本总数</div>
            </div>
        </div>
    </div>

    <div class="card">
        <h2><i class="fas fa-calendar-day"></i> 今日数据</h2>
        
        <div class="stats-grid">
            <div class="stat-item">
                <div class="value"><?php echo number_format($today_keys); ?></div>
                <div class="label">今日新增卡密</div>
            </div>
            <div class="stat-item">
                <div class="value"><?php echo number_format($today_active); ?></div>
                <div class="label">今日活跃用户</div>
            </div>
            <div class="stat-item">
                <div class="value"><?php echo number_format($week_keys); ?></div>
                <div class="label">本周新增</div>
            </div>
            <div class="stat-item">
                <div class="value"><?php echo number_format($week_active); ?></div>
                <div class="label">本周活跃</div>
            </div>
        </div>
    </div>

    <div class="card">
        <h2><i class="fas fa-chart-bar"></i> 卡密类型分布</h2>
        
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>卡密类型</th>
                        <th>总数量</th>
                        <th>有效数量</th>
                        <th>有效率</th>
                    </tr>
                </thead>
                <tbody>
                    <?php 
                    $type_names = [
                        'yearly' => '年卡',
                        'monthly' => '月卡',
                        'weekly' => '周卡',
                        'daily' => '日卡'
                    ];
                    foreach ($type_distribution as $data): 
                        $rate = $data['total'] > 0 ? round(($data['active'] / $data['total']) * 100, 1) : 0;
                    ?>
                    <tr>
                        <td><strong><?php echo $type_names[$data['type']] ?? $data['type']; ?></strong></td>
                        <td><?php echo number_format($data['total']); ?></td>
                        <td><?php echo number_format($data['active']); ?></td>
                        <td>
                            <span class="status-badge <?php echo $rate >= 80 ? 'status-active' : ($rate >= 50 ? 'status-warning' : 'status-expired'); ?>">
                                <?php echo $rate; ?>%
                            </span>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>

    <div class="card">
        <h2><i class="fas fa-chart-area"></i> 近7天趋势</h2>
        
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>日期</th>
                        <th>新增卡密</th>
                        <th>活跃用户</th>
                        <th>活跃率</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($daily_data as $data): 
                        $rate = $data['new'] > 0 ? round(($data['active'] / $data['new']) * 100, 1) : 0;
                    ?>
                    <tr>
                        <td><strong><?php echo date('m-d', strtotime($data['date'])); ?></strong></td>
                        <td><?php echo number_format($data['new']); ?></td>
                        <td><?php echo number_format($data['active']); ?></td>
                        <td>
                            <?php if ($data['new'] > 0): ?>
                                <span class="status-badge <?php echo $rate >= 50 ? 'status-active' : 'status-warning'; ?>">
                                    <?php echo $rate; ?>%
                                </span>
                            <?php else: ?>
                                <span style="opacity: 0.5;">-</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div> 
</div> 