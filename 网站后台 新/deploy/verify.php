<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type, X-API-Version, X-Request-Timestamp, X-Request-Signature');

require_once '../includes/db.php';
require_once '../includes/functions.php';
require_once 'api_base.php';

// 创建API基础类实例
$api = new ApiBase();

// 接收请求
$request_body = file_get_contents('php://input');
$client_ip = get_client_ip();

try {
    // 解析请求数据（处理加密和JSON请求）
    $request_data = $api->parseRequest($request_body);
    
    // 如果是传统表单提交方式（向后兼容）
    if (empty($request_data) && !empty($_POST['key'])) {
        $request_data = $_POST;
    }
    
    if (empty($request_data)) {
        throw new Exception('无效的请求数据');
    }
    
    // 获取请求参数
    $key = $request_data['key'] ?? '';
    $shop_info = $request_data['shop_info'] ?? [];
    $shop_name = isset($shop_info['name']) ? $shop_info['name'] : ($request_data['shop_name'] ?? '');
    $shop_id = isset($shop_info['id']) ? $shop_info['id'] : ($request_data['shop_id'] ?? '');
    $check_status = $request_data['check_status'] ?? '';
    $auto_verify = $request_data['auto_verify'] ?? false;
    $client_info = $request_data['client_info'] ?? [];

    // 验证卡密
    if (empty($key)) {
        $api->logApiCall('', $client_ip, 'verify', 'error', '卡密不能为空');
        throw new Exception('卡密不能为空');
    }

    // 检查卡密
    $license = $api->getLicenseByKey($key);

    if (!$license) {
        $api->logApiCall($key, $client_ip, 'verify', 'error', '卡密不存在');
        throw new Exception('卡密不存在', 404);
    }

    if ($license['status'] !== 'active') {
        $error_code = 'KEY_DISABLED';
        $message = '卡密状态异常: ' . $license['status'];
        
        switch ($license['status']) {
            case 'disabled':
                $error_code = 'KEY_DISABLED';
                $message = '您的卡密已经被禁用，如有疑问请联系代理商';
                break;
            case 'expired':
                $error_code = 'KEY_EXPIRED';
                $message = '您的卡密已经过期，如需继续使用请联系代理商';
                break;
            case 'suspended':
                $error_code = 'KEY_DISABLED';
                $message = '您的卡密已被暂停使用，如有疑问请联系代理商';
                break;
        }
        
        $api->logApiCall($key, $client_ip, 'verify', 'error', $message);
        throw new Exception($message, 403);
    }

    // 检查过期时间
    $now = new DateTime();
    $expiry_date = new DateTime($license['expiry_date']);

    if ($now > $expiry_date) {
        // 自动将过期的卡密状态设为 expired
        $stmt = $pdo->prepare("UPDATE license_keys SET status = 'expired' WHERE id = :id");
        $stmt->execute([':id' => $license['id']]);
        
        $message = '您的卡密已经过期，如需继续使用请联系代理商';
        $api->logApiCall($key, $client_ip, 'verify', 'error', $message);
        throw new Exception($message, 403);
    }

    // 添加店铺信息验证
    if (!empty($license['store_name']) || !empty($license['wechat_store_id'])) {
        $db_shop_name = $license['store_name'];
        $db_shop_id = $license['wechat_store_id'];
        
        // 检查店铺名称匹配
        if (!empty($db_shop_name) && !empty($shop_name)) {
            if (trim($db_shop_name) !== trim($shop_name)) {
                $api->logApiCall($key, $client_ip, 'verify', 'error', '店铺名称不匹配');
                throw new Exception('卡密与店铺信息不一致，无法登录', 403);
            }
        }
        
        // 检查店铺ID匹配
        if (!empty($db_shop_id) && !empty($shop_id)) {
            if (trim($db_shop_id) !== trim($shop_id)) {
                $api->logApiCall($key, $client_ip, 'verify', 'error', '店铺ID不匹配');
                throw new Exception('卡密与店铺信息不一致，无法登录', 403);
            }
        }
        
        $api->logApiCall($key, $client_ip, 'verify', 'info', "店铺信息验证通过: 数据库({$db_shop_name}, {$db_shop_id}) vs 提交({$shop_name}, {$shop_id})");
    }

    // 验证通过, 检查卡密功能权限
    $has_customer_service = $license['has_customer_service'] ?? 0;
    $has_product_listing = $license['has_product_listing'] ?? 0;

    if (!$has_customer_service && !$has_product_listing) {
        $api->logApiCall($key, $client_ip, 'verify', 'error', '此卡密没有任何功能权限');
        throw new Exception('此卡密没有任何功能权限', 404);
    }

    // 根据卡密功能获取对应的脚本代码
    $script_conditions = [];

    // 如果卡密有微信小店功能，获取微信小店脚本
    if ($has_customer_service) {
        $script_conditions[] = "has_wechat_store = 1";
    }

    // 如果卡密有抖店功能，获取抖店脚本
    if ($has_product_listing) {
        $script_conditions[] = "has_douyin_store = 1";
    }

    $script_sql = "
        SELECT script_code, content, name, version
        FROM scripts
        WHERE status = 'active' AND (" . implode(' OR ', $script_conditions) . ")
        ORDER BY updated_at DESC
        LIMIT 1
    ";

    $script_stmt = $pdo->prepare($script_sql);
    $script_stmt->execute();
    $script_row = $script_stmt->fetch(PDO::FETCH_ASSOC);

    if (!$script_row) {
        $api->logApiCall($key, $client_ip, 'verify', 'error', '找不到对应功能的脚本');
        throw new Exception('找不到对应功能的脚本', 404);
    }

    // 优先使用script_code，如果没有则使用content
    $script_code = $script_row['script_code'] ?? $script_row['content'] ?? '';

    if (empty($script_code)) {
        $api->logApiCall($key, $client_ip, 'verify', 'error', '脚本代码为空');
        throw new Exception('脚本代码为空', 404);
    }



    // 【升级】如果是状态检查请求，只返回状态信息，不返回脚本代码
    if ($check_status === '1') {
        // 确定功能类型
        $has_customer_service = $license['has_customer_service'] ?? 1;
        $has_product_listing = $license['has_product_listing'] ?? 0;
        
        $function_type = '';
        if ($has_customer_service && $has_product_listing) {
            $function_type = 'full_features';
        } elseif ($has_product_listing && !$has_customer_service) {
            $function_type = 'product_listing';
        } elseif ($has_customer_service && !$has_product_listing) {
            $function_type = 'customer_service';
        } else {
            $function_type = 'no_features';
        }
        
        $api->logApiCall($key, $client_ip, 'verify', 'success', "状态检查成功 - 功能类型: {$function_type}");
        
        // 生成安全令牌
        $security_token = $api->generateSecurityToken($key, $license['id']);
        
        $response = [
            'success' => true,
            'message' => '卡密状态正常',
            'function_type' => $function_type,
            'has_customer_service' => (bool)$has_customer_service,
            'has_product_listing' => (bool)$has_product_listing,
            'expiry_date' => $license['expiry_date'],
            'status' => $license['status'],
            'security_token' => $security_token
        ];
        
        echo $api->encryptResponse($response);
        exit;
    }

    // 更新IP地址和最后心跳时间
    $update_ip_stmt = $pdo->prepare("
        UPDATE license_keys 
        SET last_used_ip = :ip, 
            last_heartbeat = NOW(),
            client_info = :client_info
        WHERE id = :id
    ");
    
    $client_info_json = !empty($client_info) ? json_encode($client_info) : null;
    
    $update_ip_stmt->execute([
        ':ip' => $client_ip,
        ':client_info' => $client_info_json,
        ':id' => $license['id']
    ]);

    // 确定功能类型和提示信息
    $has_customer_service = $license['has_customer_service'] ?? 1;
    $has_product_listing = $license['has_product_listing'] ?? 0;
    
    $function_type = '';
    $success_message = '';
    
    if ($has_customer_service && $has_product_listing) {
        $function_type = 'full_features';
        $success_message = '卡密验证通过，恭喜您已成功开通"<span style="color: #ff0000; font-weight: bold;">小梅花AI全功能</span>"，感谢您选择小梅花AI智能客服系统！';
    } elseif ($has_product_listing && !$has_customer_service) {
        $function_type = 'product_listing';
        $success_message = '卡密验证通过，恭喜您已成功开通"<span style="color: #ff0000; font-weight: bold;">微信小店上架产品功能</span>"，感谢您选择小梅花AI智能客服系统！';
    } elseif ($has_customer_service && !$has_product_listing) {
        $function_type = 'customer_service';
        $success_message = '卡密验证通过，恭喜您已成功开通"<span style="color: #ff0000; font-weight: bold;">AI智能客服回复功能</span>"，感谢您选择小梅花AI智能客服系统！';
    } else {
        $function_type = 'no_features';
        $success_message = '卡密验证通过，但此卡密未开通任何功能，请联系管理员。';
    }
    
    // 生成安全令牌
    $security_token = $api->generateSecurityToken($key, $license['id']);

    $api->logApiCall($key, $client_ip, 'verify', 'success', "验证成功 - 功能类型: {$function_type}, 自动验证: " . ($auto_verify ? '是' : '否'));
    
    // 构建返回数据
    $response_data = [
        'success' => true,
        'script' => $script_code,
        'expiry_date' => $license['expiry_date'],
        'type' => $license['type'],
        'store_name' => $license['store_name'],
        'wechat_store_id' => $license['wechat_store_id'],
        'function_type' => $function_type,
        'message' => $success_message,
        'has_customer_service' => (bool)$has_customer_service,
        'has_product_listing' => (bool)$has_product_listing,
        'security_token' => $security_token
    ];


    
    // 返回响应
    echo $api->encryptResponse($response_data);

} catch (Exception $e) {
    $error_code = $e->getCode() ?: 500;
    $error_message = $e->getMessage();
    
    $api->logApiCall(isset($key) ? $key : 'unknown', $client_ip, 'verify', 'error', "错误 [{$error_code}]: {$error_message}");
    
    $response = [
        'success' => false, 
        'message' => $error_message,
        'code' => $error_code
    ];
    
    if ($error_code == 404) {
        $response['error_code'] = 'KEY_DELETED';
    } elseif ($error_code == 403) {
        if (strpos($error_message, '过期') !== false) {
            $response['error_code'] = 'KEY_EXPIRED';
        } else {
            $response['error_code'] = 'KEY_DISABLED';
        }
    }
    
    // 错误信息不加密
    echo json_encode($response);
} 