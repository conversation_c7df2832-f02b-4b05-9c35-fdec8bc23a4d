-- 数据库升级脚本：添加卡密功能权限字段
-- 执行日期：2024年

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- USE xuxuemei; (已注释：避免数据库切换问题)

-- 检查并添加has_customer_service字段（如果不存在）
SET @sql = (SELECT IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = 'license_keys' AND table_schema = DATABASE() AND column_name = 'has_customer_service') > 0, 'SELECT "has_customer_service字段已存在"', 'ALTER TABLE `license_keys` ADD COLUMN `has_customer_service` tinyint(1) NOT NULL DEFAULT 1 COMMENT "小梅花AI客服-微信小店"'));
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- 检查并添加has_product_listing字段（如果不存在）
SET @sql = (SELECT IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = 'license_keys' AND table_schema = DATABASE() AND column_name = 'has_product_listing') > 0, 'SELECT "has_product_listing字段已存在"', 'ALTER TABLE `license_keys` ADD COLUMN `has_product_listing` tinyint(1) NOT NULL DEFAULT 0 COMMENT "小梅花AI客服-抖店"'));
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
-- 为现有卡密设置默认值（所有现有卡密默认开通客服功能）
UPDATE `license_keys` SET 
    `has_customer_service` = 1,
    `has_product_listing` = 0 
WHERE `has_customer_service` IS NULL OR `has_product_listing` IS NULL;

-- 检查并添加idx_features索引（如果不存在）
SET @sql = (SELECT IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name = 'license_keys' AND table_schema = DATABASE() AND index_name = 'idx_features') > 0, 'SELECT "idx_features索引已存在"', 'ALTER TABLE `license_keys` ADD INDEX `idx_features` (`has_customer_service`, `has_product_listing`)'));
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
-- 创建测试数据（可选）
-- INSERT INTO `license_keys` (...) VALUES (...); (已注释掉不完整的INSERT语句)

SET FOREIGN_KEY_CHECKS = 1;

SELECT 'Database upgrade completed successfully!' AS message; 


-- 功能升级完成
SELECT "功能升级脚本执行完成" as message;