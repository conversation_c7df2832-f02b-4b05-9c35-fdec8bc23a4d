/**
 * 安全API客户端
 * 版本: v2.0.0
 * 功能: 前端安全API调用，替换直接数据库连接
 */

class SecureApiClient {
    constructor(baseUrl = '/api/gateway.php') {
        this.baseUrl = baseUrl;
        this.accessToken = localStorage.getItem('accessToken');
        this.refreshToken = localStorage.getItem('refreshToken');
        this.isRefreshing = false;
        this.failedQueue = [];
    }

    /**
     * 检查令牌是否过期
     */
    isTokenExpired(token) {
        if (!token) return true;
        
        try {
            const payload = JSON.parse(atob(token.split('.')[1]));
            return payload.exp < Date.now() / 1000;
        } catch (e) {
            return true;
        }
    }

    /**
     * 刷新访问令牌
     */
    async refreshAccessToken() {
        if (this.isRefreshing) {
            return new Promise((resolve, reject) => {
                this.failedQueue.push({ resolve, reject });
            });
        }

        this.isRefreshing = true;

        try {
            const response = await fetch(`${this.baseUrl}/auth/refresh`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    refresh_token: this.refreshToken
                })
            });

            const data = await response.json();

            if (data.success) {
                this.accessToken = data.access_token;
                localStorage.setItem('accessToken', this.accessToken);
                
                // 处理队列中的请求
                this.failedQueue.forEach(({ resolve }) => {
                    resolve(this.accessToken);
                });
                this.failedQueue = [];
                
                return this.accessToken;
            } else {
                throw new Error(data.message || '令牌刷新失败');
            }
        } catch (error) {
            // 刷新失败，清除令牌并跳转到登录页
            this.clearTokens();
            this.failedQueue.forEach(({ reject }) => {
                reject(error);
            });
            this.failedQueue = [];
            
            // 跳转到登录页
            if (typeof window !== 'undefined') {
                window.location.href = '/login.php';
            }
            
            throw error;
        } finally {
            this.isRefreshing = false;
        }
    }

    /**
     * 获取有效的访问令牌
     */
    async getValidToken() {
        if (!this.accessToken || this.isTokenExpired(this.accessToken)) {
            return await this.refreshAccessToken();
        }
        return this.accessToken;
    }

    /**
     * 安全API请求
     */
    async request(endpoint, options = {}) {
        const {
            method = 'GET',
            data = null,
            headers = {},
            requireAuth = true
        } = options;

        const requestHeaders = {
            'Content-Type': 'application/json',
            ...headers
        };

        // 添加认证头
        if (requireAuth) {
            try {
                const token = await this.getValidToken();
                requestHeaders['Authorization'] = `Bearer ${token}`;
            } catch (error) {
                throw new Error('认证失败');
            }
        }

        const requestOptions = {
            method,
            headers: requestHeaders
        };

        if (data && (method === 'POST' || method === 'PUT')) {
            requestOptions.body = JSON.stringify(data);
        }

        try {
            const response = await fetch(`${this.baseUrl}${endpoint}`, requestOptions);
            const responseData = await response.json();

            if (!response.ok) {
                throw new Error(responseData.message || `HTTP ${response.status}`);
            }

            return responseData;
        } catch (error) {
            console.error('API请求失败:', error);
            throw error;
        }
    }

    /**
     * 用户登录
     */
    async login(username, password) {
        try {
            const response = await this.request('/auth/login', {
                method: 'POST',
                data: { username, password },
                requireAuth: false
            });

            if (response.success) {
                this.accessToken = response.access_token;
                this.refreshToken = response.refresh_token;
                
                localStorage.setItem('accessToken', this.accessToken);
                localStorage.setItem('refreshToken', this.refreshToken);
            }

            return response;
        } catch (error) {
            throw error;
        }
    }

    /**
     * 用户登出
     */
    async logout() {
        try {
            await this.request('/auth/logout', {
                method: 'POST'
            });
        } catch (error) {
            console.warn('登出请求失败:', error);
        } finally {
            this.clearTokens();
        }
    }

    /**
     * 清除令牌
     */
    clearTokens() {
        this.accessToken = null;
        this.refreshToken = null;
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');
    }

    // ==================== 业务API方法 ====================

    /**
     * 验证卡密
     */
    async verifyKey(key, shopInfo = {}) {
        return await this.request('/api/verify', {
            method: 'POST',
            data: {
                key: key,
                shop_name: shopInfo.name || '',
                shop_id: shopInfo.id || ''
            },
            requireAuth: false
        });
    }

    /**
     * 获取卡密列表
     */
    async getKeys(page = 1, limit = 20) {
        return await this.request(`/api/keys?page=${page}&limit=${limit}`);
    }

    /**
     * 创建卡密
     */
    async createKey(keyData) {
        return await this.request('/api/keys', {
            method: 'POST',
            data: keyData
        });
    }

    /**
     * 更新卡密
     */
    async updateKey(id, keyData) {
        return await this.request(`/api/keys/${id}`, {
            method: 'PUT',
            data: keyData
        });
    }

    /**
     * 删除卡密
     */
    async deleteKey(id) {
        return await this.request(`/api/keys/${id}`, {
            method: 'DELETE'
        });
    }

    /**
     * 获取脚本列表
     */
    async getScripts() {
        return await this.request('/api/scripts');
    }

    /**
     * 创建脚本
     */
    async createScript(scriptData) {
        return await this.request('/api/scripts', {
            method: 'POST',
            data: scriptData
        });
    }

    /**
     * 更新脚本
     */
    async updateScript(id, scriptData) {
        return await this.request(`/api/scripts/${id}`, {
            method: 'PUT',
            data: scriptData
        });
    }

    /**
     * 删除脚本
     */
    async deleteScript(id) {
        return await this.request(`/api/scripts/${id}`, {
            method: 'DELETE'
        });
    }

    /**
     * 获取用户列表
     */
    async getUsers() {
        return await this.request('/api/users');
    }

    /**
     * 获取分析数据
     */
    async getAnalytics() {
        return await this.request('/api/analytics');
    }

    /**
     * 获取系统设置
     */
    async getSettings() {
        return await this.request('/api/settings');
    }

    /**
     * 更新系统设置
     */
    async updateSettings(settings) {
        return await this.request('/api/settings', {
            method: 'PUT',
            data: settings
        });
    }
}

// ==================== Tampermonkey 兼容层 ====================

/**
 * Tampermonkey脚本专用的安全API调用
 */
class TampermonkeySecureApi {
    constructor(baseUrl = 'https://xiaomeihuakefu.cn/api/gateway.php') {
        this.baseUrl = baseUrl;
    }

    /**
     * 安全的卡密验证（替换原有的不安全方式）
     */
    verifyKey(key, shopInfo = {}) {
        return new Promise((resolve, reject) => {
            // 使用GM_xmlhttpRequest替代不安全的直接数据库连接
            GM_xmlhttpRequest({
                method: 'POST',
                url: `${this.baseUrl}/api/verify`,
                headers: {
                    'Content-Type': 'application/json'
                },
                data: JSON.stringify({
                    key: key,
                    shop_name: shopInfo.name || '',
                    shop_id: shopInfo.id || ''
                }),
                onload: function(response) {
                    try {
                        const data = JSON.parse(response.responseText);
                        resolve(data);
                    } catch (error) {
                        reject(new Error('响应解析失败'));
                    }
                },
                onerror: function(error) {
                    reject(new Error('网络请求失败'));
                }
            });
        });
    }

    /**
     * 获取脚本内容
     */
    getScript(scriptId) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: 'GET',
                url: `${this.baseUrl}/api/scripts/${scriptId}`,
                onload: function(response) {
                    try {
                        const data = JSON.parse(response.responseText);
                        resolve(data);
                    } catch (error) {
                        reject(new Error('响应解析失败'));
                    }
                },
                onerror: function(error) {
                    reject(new Error('网络请求失败'));
                }
            });
        });
    }

    /**
     * 记录用户活动
     */
    logActivity(activityData) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: 'POST',
                url: `${this.baseUrl}/api/activity`,
                headers: {
                    'Content-Type': 'application/json'
                },
                data: JSON.stringify(activityData),
                onload: function(response) {
                    try {
                        const data = JSON.parse(response.responseText);
                        resolve(data);
                    } catch (error) {
                        reject(new Error('响应解析失败'));
                    }
                },
                onerror: function(error) {
                    reject(new Error('网络请求失败'));
                }
            });
        });
    }
}

// ==================== 使用示例 ====================

/**
 * 管理后台使用示例
 */
/*
// 初始化API客户端
const apiClient = new SecureApiClient();

// 登录
apiClient.login('admin', 'password').then(response => {
    if (response.success) {
        console.log('登录成功');
        // 获取卡密列表
        return apiClient.getKeys();
    }
}).then(response => {
    console.log('卡密列表:', response.data);
}).catch(error => {
    console.error('操作失败:', error);
});
*/

/**
 * Tampermonkey脚本使用示例
 */
/*
// 初始化Tampermonkey API
const tmApi = new TampermonkeySecureApi();

// 验证卡密
tmApi.verifyKey('your-license-key', {
    name: '店铺名称',
    id: '店铺ID'
}).then(response => {
    if (response.success) {
        console.log('卡密验证成功');
        // 获取脚本内容
        return tmApi.getScript(response.script_id);
    }
}).then(scriptData => {
    if (scriptData.success) {
        // 执行脚本
        eval(scriptData.script_code);
    }
}).catch(error => {
    console.error('验证失败:', error);
});
*/

// 导出类（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        SecureApiClient,
        TampermonkeySecureApi
    };
}

// 全局暴露（浏览器环境）
if (typeof window !== 'undefined') {
    window.SecureApiClient = SecureApiClient;
    window.TampermonkeySecureApi = TampermonkeySecureApi;
} 