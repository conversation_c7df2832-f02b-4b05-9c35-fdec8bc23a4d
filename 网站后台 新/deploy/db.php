<?php
// 导入环境变量管理系统
require_once dirname(__DIR__) . '/api/env_manager.php';

// 初始化环境管理器
$env_manager = EnvManager::getInstance();
$env_manager->load();

// === 使用环境变量获取数据库连接配置 ===
// 自动从环境变量、.env文件或现有配置中获取
$mysql_config = $env_manager->getDatabaseConfig();

// 添加默认值和额外配置 - 移除所有硬编码的凭证
$mysql_config = [
    'remote_host' => $mysql_config['host'] ?? 'localhost',
    'local_host' => 'localhost',
    'port' => $mysql_config['port'] ?? '3306',
    'dbname' => $mysql_config['dbname'] ?? '',
    'username' => $mysql_config['user'] ?? '',
    'password' => $mysql_config['pass'] ?? '',
    'charset' => 'utf8mb4'
];

// 尝试连接数据库 - 优先远程，失败则本地
$connection_success = false;
$connection_attempts = [
    [
        'name' => '环境变量配置', 
        'host' => $mysql_config['remote_host']
    ],
    [
        'name' => '本地连接', 
        'host' => $mysql_config['local_host']
    ]
];

foreach ($connection_attempts as $attempt) {
    try {
        $dsn = "mysql:host={$attempt['host']};port={$mysql_config['port']};dbname={$mysql_config['dbname']};charset={$mysql_config['charset']}";
        $pdo = new PDO($dsn, $mysql_config['username'], $mysql_config['password'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_TIMEOUT => 10,
            // 启用持久连接，优化性能
            PDO::ATTR_PERSISTENT => true,
            // 禁用预处理语句模拟，确保使用真正的预处理语句
            PDO::ATTR_EMULATE_PREPARES => false
        ]);
        
        // 连接成功，保存当前使用的配置到环境变量（如果是第一次连接）
        if (!file_exists(dirname(__DIR__) . '/.env') && $connection_success === false) {
            $env_manager->saveDatabaseConfig([
                'host' => $attempt['host'],
                'port' => $mysql_config['port'],
                'dbname' => $mysql_config['dbname'],
                'user' => $mysql_config['username'],
                'pass' => $mysql_config['password']
            ]);
        }
        
        $connection_success = true;
        error_log("数据库连接成功: {$attempt['name']} ({$attempt['host']})");
        break;
        
    } catch (PDOException $e) {
        error_log("数据库连接失败 - {$attempt['name']} ({$attempt['host']}): " . $e->getMessage());
        continue;
    }
}

// 如果所有连接尝试都失败，显示通用错误消息
if (!$connection_success) {
    if (php_sapi_name() !== 'cli') {
        header('HTTP/1.1 503 Service Unavailable');
        echo '<div style="color:red;font-weight:bold;text-align:center;margin-top:50px;">
            系统维护中，请稍后再试。
            <br><small>Database connection unavailable</small>
        </div>';
    } else {
        echo "数据库连接失败 - 请检查环境变量或.env文件中的数据库配置\n";
    }
    exit;
}

/**
 * 执行安全的预处理SQL查询
 * 
 * @param string $sql SQL查询语句
 * @param array $params 绑定参数
 * @return PDOStatement 预处理语句
 */
function db_query($sql, $params = []) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt;
    } catch (PDOException $e) {
        error_log("SQL查询错误: " . $e->getMessage() . " - SQL: $sql");
        throw $e;
    }
}

/**
 * 获取单行结果
 * 
 * @param string $sql SQL查询语句
 * @param array $params 绑定参数
 * @return array|false 结果行或false
 */
function db_get_row($sql, $params = []) {
    $stmt = db_query($sql, $params);
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

/**
 * 获取所有结果行
 * 
 * @param string $sql SQL查询语句
 * @param array $params 绑定参数
 * @return array 结果集
 */
function db_get_all($sql, $params = []) {
    $stmt = db_query($sql, $params);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * 执行插入、更新或删除操作
 * 
 * @param string $sql SQL查询语句
 * @param array $params 绑定参数
 * @return int 受影响的行数
 */
function db_execute($sql, $params = []) {
    $stmt = db_query($sql, $params);
    return $stmt->rowCount();
}

/**
 * 获取最后插入的ID
 * 
 * @return string 最后插入的ID
 */
function db_last_insert_id() {
    global $pdo;
    return $pdo->lastInsertId();
}

// 创建基本的管理员用户表（MySQL语法）
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `admin_users` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `username` varchar(50) NOT NULL UNIQUE,
            `password` varchar(255) NOT NULL,
            `email` varchar(100) DEFAULT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `username` (`username`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ");
    
    // 创建API密钥表（MySQL语法）
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `api_keys` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `key_name` varchar(255) NOT NULL,
            `api_key` varchar(255) NOT NULL,
            `is_active` tinyint(1) DEFAULT 1,
            `created_by` int(11) DEFAULT 1,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `api_key` (`api_key`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ");
    
    // 创建JWT令牌表（MySQL语法）
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `jwt_tokens` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `token_name` varchar(255) NOT NULL,
            `jwt_token` text NOT NULL,
            `is_active` tinyint(1) DEFAULT 1,
            `expires_at` timestamp NULL DEFAULT NULL,
            `created_by` int(11) DEFAULT 1,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ");
    

    
    // 检查是否有管理员用户，如果没有则创建默认用户
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM admin_users");
    $stmt->execute();
    $result = $stmt->fetch();
    
    if ($result['count'] == 0) {
        // 创建默认管理员账户
        $defaultPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("
            INSERT INTO admin_users (username, password, email) 
            VALUES (?, ?, ?)
        ");
        $stmt->execute(['admin', $defaultPassword, '<EMAIL>']);
    }
    
} catch (PDOException $e) {
    error_log("数据库表创建失败: " . $e->getMessage());
}
?> 