-- 小梅花智能AI客服系统 - 紧急修复脚本 (清理版)
-- 版本: v2.1.0 - 完全重写版
-- 日期: 2024-06-30
-- 功能: 修复数据库问题，解决常见错误
-- 兼容: MySQL 5.7+ 和宝塔环境

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ====================================
-- 基础表结构修复
-- ====================================

-- 1. 创建管理员用户表（如果不存在）
CREATE TABLE IF NOT EXISTS `admin_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_login` timestamp NULL DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员用户表';

-- 插入默认管理员用户（如果不存在）
INSERT IGNORE INTO `admin_users` (`username`, `password`, `email`) VALUES 
('admin', 'e10adc3949ba59abbe56e057f20f883e', '<EMAIL>');

-- 2. 创建系统设置表（如果不存在）
CREATE TABLE IF NOT EXISTS `system_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text,
  `description` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统设置表';

-- 插入默认系统设置（如果不存在）
INSERT IGNORE INTO `system_settings` (`setting_key`, `setting_value`, `description`) VALUES
('smtp_host', 'smtp.163.com', 'SMTP服务器地址'),
('smtp_port', '465', 'SMTP端口'),
('smtp_username', '', 'SMTP用户名'),
('smtp_password', '', 'SMTP密码'),
('from_email', '', '发件人邮箱'),
('from_name', '小梅花AI客服系统', '发件人名称'),
('email_verification_enabled', '0', '是否启用邮箱验证'),
('login_notification_enabled', '0', '是否启用登录通知');

-- ====================================
-- 安全验证表修复
-- ====================================

-- 3. 创建SMTP配置表（如果不存在）
CREATE TABLE IF NOT EXISTS `smtp_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `smtp_host` varchar(255) NOT NULL,
  `smtp_port` int(11) NOT NULL DEFAULT 465,
  `smtp_username` varchar(255) NOT NULL,
  `smtp_password` varchar(255) NOT NULL,
  `from_email` varchar(255) DEFAULT NULL,
  `from_name` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_smtp` (`user_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='SMTP配置表';

-- 4. 创建信任设备表（如果不存在）
CREATE TABLE IF NOT EXISTS `trusted_devices` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `device_name` varchar(255) NOT NULL,
  `device_fingerprint` varchar(255) NOT NULL,
  `user_agent` text,
  `ip_address` varchar(45) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_device` (`user_id`, `device_fingerprint`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_fingerprint` (`device_fingerprint`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='信任设备表';

-- 5. 创建验证码表（如果不存在）
CREATE TABLE IF NOT EXISTS `verification_codes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `code` varchar(6) NOT NULL,
  `type` varchar(50) NOT NULL,
  `expires_at` timestamp NOT NULL,
  `is_used` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_code` (`user_id`, `code`),
  KEY `idx_expires` (`expires_at`),
  KEY `idx_type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='验证码表';

-- 6. 创建登录日志表（如果不存在）
CREATE TABLE IF NOT EXISTS `login_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  `device_info` text,
  `device_fingerprint` varchar(255) DEFAULT NULL,
  `login_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `login_status` enum('success','failed','blocked') NOT NULL DEFAULT 'success',
  `is_trusted_device` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_login_time` (`login_time`),
  KEY `idx_status` (`login_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='登录日志表';

-- ====================================
-- API支持表修复
-- ====================================

-- 7. 创建API日志表（如果不存在）
CREATE TABLE IF NOT EXISTS `api_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `endpoint` varchar(100) NOT NULL,
  `method` varchar(10) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` text,
  `request_data` text,
  `response_data` text,
  `status_code` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_endpoint` (`endpoint`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_status_code` (`status_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='API日志表';

-- ====================================
-- 数据完整性修复
-- ====================================

-- 清理过期数据（安全操作）
DELETE FROM `verification_codes` WHERE `expires_at` < NOW() AND EXISTS (SELECT 1 FROM `verification_codes` LIMIT 1);
DELETE FROM `login_logs` WHERE `login_time` < DATE_SUB(NOW(), INTERVAL 30 DAY) AND EXISTS (SELECT 1 FROM `login_logs` LIMIT 1);
DELETE FROM `api_logs` WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 7 DAY) AND EXISTS (SELECT 1 FROM `api_logs` LIMIT 1);

-- ====================================
-- 修复完成验证
-- ====================================

SET FOREIGN_KEY_CHECKS = 1;

SELECT '数据库紧急修复完成！' as message; 